const mysql = require('mysql2/promise');

async function testQuoteTables() {
  let connection;
  
  try {
    // إنشاء الاتصال
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'droobhajer_db'
    });

    console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');

    // التحقق من وجود جداول طلبات التسعير
    const tables = ['quote_requests', 'quote_request_products', 'quote_request_logs'];
    
    for (const table of tables) {
      try {
        const [rows] = await connection.execute(`DESCRIBE ${table}`);
        console.log(`✅ جدول ${table} موجود ويحتوي على ${rows.length} عمود`);
        
        // عرض بنية الجدول
        console.log(`📋 بنية جدول ${table}:`);
        rows.forEach(row => {
          console.log(`   - ${row.Field}: ${row.Type} ${row.Null === 'NO' ? '(مطلوب)' : '(اختياري)'}`);
        });
        console.log('');
        
      } catch (error) {
        console.log(`❌ جدول ${table} غير موجود: ${error.message}`);
      }
    }

    // اختبار إدراج طلب تسعير تجريبي
    console.log('🧪 اختبار إدراج طلب تسعير تجريبي...');
    
    const testQuoteId = `TEST-${Date.now()}`;
    
    try {
      await connection.execute(`
        INSERT INTO quote_requests (
          id, customer_name, customer_email, customer_phone, customer_company,
          excel_file_url, status, notes
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        testQuoteId,
        'عميل تجريبي',
        '<EMAIL>',
        '966501234567',
        'شركة تجريبية',
        'uploads/excel/test.xlsx',
        'pending',
        'طلب تجريبي للاختبار'
      ]);
      
      console.log(`✅ تم إدراج طلب التسعير التجريبي: ${testQuoteId}`);
      
      // إدراج منتج تجريبي للطلب
      await connection.execute(`
        INSERT INTO quote_request_products (quote_id, product_id)
        VALUES (?, ?)
      `, [testQuoteId, '1']);
      
      console.log('✅ تم إدراج منتج تجريبي للطلب');
      
      // إدراج سجل تجريبي
      await connection.execute(`
        INSERT INTO quote_request_logs (quote_id, action_by, action_type, note)
        VALUES (?, ?, ?, ?)
      `, [testQuoteId, 'admin', 'note', 'تم إنشاء الطلب']);
      
      console.log('✅ تم إدراج سجل تجريبي للطلب');
      
      // جلب البيانات للتأكد
      const [quoteData] = await connection.execute(`
        SELECT * FROM quote_requests WHERE id = ?
      `, [testQuoteId]);
      
      console.log('📊 بيانات الطلب التجريبي:', quoteData[0]);
      
      // حذف البيانات التجريبية
      await connection.execute(`DELETE FROM quote_request_logs WHERE quote_id = ?`, [testQuoteId]);
      await connection.execute(`DELETE FROM quote_request_products WHERE quote_id = ?`, [testQuoteId]);
      await connection.execute(`DELETE FROM quote_requests WHERE id = ?`, [testQuoteId]);
      
      console.log('🗑️ تم حذف البيانات التجريبية');
      
    } catch (error) {
      console.log(`❌ خطأ في اختبار الإدراج: ${error.message}`);
    }

    console.log('\n🎉 انتهى اختبار جداول طلبات التسعير بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

testQuoteTables();
