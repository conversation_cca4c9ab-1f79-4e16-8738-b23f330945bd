const fetch = require('node-fetch');

async function testQuoteAPI() {
  const baseURL = 'http://localhost:3000';
  
  console.log('🧪 بدء اختبار API طلبات التسعير...\n');

  try {
    // 1. اختبار جلب طلبات التسعير
    console.log('1️⃣ اختبار جلب طلبات التسعير...');
    const getResponse = await fetch(`${baseURL}/api/quote-requests`);
    const getResult = await getResponse.json();
    
    if (getResult.success) {
      console.log(`✅ تم جلب ${getResult.requests.length} طلب تسعير`);
      if (getResult.requests.length > 0) {
        console.log('📋 أول طلب:', {
          id: getResult.requests[0].id,
          customerName: getResult.requests[0].customerInfo?.name || getResult.requests[0].customer_name,
          status: getResult.requests[0].status
        });
      }
    } else {
      console.log('❌ فشل في جلب طلبات التسعير:', getResult.message);
    }

    // 2. اختبار إنشاء طلب تسعير جديد
    console.log('\n2️⃣ اختبار إنشاء طلب تسعير جديد...');
    
    const testQuoteData = {
      customerInfo: {
        name: 'عميل اختبار API',
        email: '<EMAIL>',
        phone: '966501234567',
        company: 'شركة اختبار API'
      },
      products: [
        {
          id: '1',
          title: 'منتج اختبار',
          titleAr: 'منتج اختبار',
          price: 100,
          quantity: 2,
          image: '/test-image.jpg'
        }
      ]
    };

    const createResponse = await fetch(`${baseURL}/api/quote-requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testQuoteData)
    });

    const createResult = await createResponse.json();
    
    if (createResult.success) {
      console.log(`✅ تم إنشاء طلب تسعير جديد: ${createResult.requestId}`);
      console.log(`📧 إرسال الإيميل: ${createResult.emailSent ? 'نجح' : 'فشل'}`);
      
      const newQuoteId = createResult.requestId;

      // 3. اختبار جلب طلب تسعير محدد
      console.log('\n3️⃣ اختبار جلب طلب التسعير المُنشأ...');
      const getOneResponse = await fetch(`${baseURL}/api/quote-requests/${newQuoteId}`);
      const getOneResult = await getOneResponse.json();
      
      if (getOneResult.success) {
        console.log('✅ تم جلب طلب التسعير بنجاح');
        console.log('📋 تفاصيل الطلب:', {
          id: getOneResult.request.id,
          customerName: getOneResult.request.customer_name,
          status: getOneResult.request.status,
          productsCount: getOneResult.request.products?.length || 0,
          logsCount: getOneResult.request.logs?.length || 0
        });
      } else {
        console.log('❌ فشل في جلب طلب التسعير:', getOneResult.message);
      }

      // 4. اختبار تحديث حالة طلب التسعير
      console.log('\n4️⃣ اختبار تحديث حالة طلب التسعير...');
      const updateResponse = await fetch(`${baseURL}/api/quote-requests/${newQuoteId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: 'processed',
          notes: 'تم معالجة الطلب عبر API'
        })
      });

      const updateResult = await updateResponse.json();
      
      if (updateResult.success) {
        console.log('✅ تم تحديث حالة طلب التسعير بنجاح');
        console.log('📋 الحالة الجديدة:', updateResult.request.status);
      } else {
        console.log('❌ فشل في تحديث طلب التسعير:', updateResult.message);
      }

    } else {
      console.log('❌ فشل في إنشاء طلب التسعير:', createResult.message);
    }

    // 5. اختبار إحصائيات لوحة التحكم
    console.log('\n5️⃣ اختبار إحصائيات لوحة التحكم...');
    const statsResponse = await fetch(`${baseURL}/api/admin-dashboard-stats`);
    const statsResult = await statsResponse.json();
    
    if (statsResult.success) {
      console.log('✅ تم جلب إحصائيات لوحة التحكم بنجاح');
      console.log('📊 إحصائيات طلبات التسعير:', statsResult.stats.quotes);
    } else {
      console.log('❌ فشل في جلب إحصائيات لوحة التحكم:', statsResult.message);
    }

    console.log('\n🎉 انتهى اختبار API طلبات التسعير بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار API:', error.message);
  }
}

// تشغيل الاختبار
testQuoteAPI();
