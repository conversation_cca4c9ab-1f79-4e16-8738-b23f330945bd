'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import Navbar from '../../../components/Navbar';
import Footer from '../../../components/Footer';
import WhatsAppButton from '../../../components/WhatsAppButton';
import toast from 'react-hot-toast';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

export default function CartPage() {
  const params = useParams();
  const locale = (params?.locale || 'ar') as Locale;
  const t = (key: string) => getTranslation(locale, key as any);

  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [form, setForm] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
  });

  const content = {
    ar: {
      title: 'سلة التسوق',
      subtitle: 'راجع المنتجات المختارة واطلب عرض سعر',
      empty: 'السلة فارغة',
      emptyMessage: 'لم تقم بإضافة أي منتجات إلى السلة بعد',
      browseProducts: 'تصفح المنتجات',
      product: 'المنتج',
      quantity: 'الكمية',
      price: 'السعر',
      total: 'المجموع',
      remove: 'حذف',
      requestQuote: 'طلب عرض سعر',
      customerInfo: 'معلومات العميل',
      name: 'الاسم الكامل',
      email: 'البريد الإلكتروني',
      phone: 'رقم الهاتف',
      company: 'اسم الشركة (اختياري)',
      send: 'إرسال الطلب',
      cancel: 'إلغاء',
      orderSent: 'تم إرسال طلبك بنجاح! سنتواصل معك قريباً.',
      currency: 'ر.س'
    },
    en: {
      title: 'Shopping Cart',
      subtitle: 'Review selected products and request a quote',
      empty: 'Cart is Empty',
      emptyMessage: 'You haven\'t added any products to your cart yet',
      browseProducts: 'Browse Products',
      product: 'Product',
      quantity: 'Quantity',
      price: 'Price',
      total: 'Total',
      remove: 'Remove',
      requestQuote: 'Request Quote',
      customerInfo: 'Customer Information',
      name: 'Full Name',
      email: 'Email Address',
      phone: 'Phone Number',
      company: 'Company Name (Optional)',
      send: 'Send Request',
      cancel: 'Cancel',
      orderSent: 'Your request has been sent successfully! We will contact you soon.',
      currency: 'SAR'
    }
  };

  const currentContent = content[locale];

  useEffect(() => {
    // جلب المنتجات من localStorage
    const items = localStorage.getItem('cart');
    if (items) {
      try {
        setCartItems(JSON.parse(items));
      } catch {
        setCartItems([]);
      }
    }
  }, []);

  const updateCart = (newItems: CartItem[]) => {
    setCartItems(newItems);
    localStorage.setItem('cart', JSON.stringify(newItems));
    window.dispatchEvent(new Event('cartUpdated'));
  };

  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(id);
      return;
    }
    const updatedItems = cartItems.map(item =>
      item.id === id ? { ...item, quantity: newQuantity } : item
    );
    updateCart(updatedItems);
  };

  const removeItem = (id: string) => {
    const updatedItems = cartItems.filter(item => item.id !== id);
    updateCart(updatedItems);
  };

  const clearCart = () => {
    updateCart([]);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSend = async (e: React.FormEvent) => {
    e.preventDefault();

    // إظهار toast التحميل
    const loadingToast = toast.loading(
      locale === 'ar' ? 'جاري إرسال طلب التسعير...' : 'Sending quote request...',
      {
        style: {
          background: '#3b82f6',
          color: '#fff',
        },
      }
    );

    try {
      // إرسال البيانات إلى API
      const response = await fetch('/api/quote-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerInfo: form,
          products: cartItems
        }),
      });

      const result = await response.json();

      if (result.success) {
        // إخفاء toast التحميل وإظهار toast النجاح
        toast.dismiss(loadingToast);
        toast.success(result.message || currentContent.orderSent, {
          duration: 6000,
          style: {
            background: '#10b981',
            color: '#fff',
          },
          iconTheme: {
            primary: '#fff',
            secondary: '#10b981',
          },
        });

        // إذا لم يتم إرسال الإيميل، أظهر تحذير إضافي
        if (!result.emailSent) {
          setTimeout(() => {
            toast.error(
              locale === 'ar'
                ? 'تنبيه: لم يتم إرسال إيميل التأكيد. سنتواصل معك عبر الهاتف.'
                : 'Warning: Confirmation email was not sent. We will contact you by phone.',
              {
                duration: 5000,
                style: {
                  background: '#f59e0b',
                  color: '#fff',
                },
              }
            );
          }, 1000);
        }

        setShowForm(false);
        setForm({ name: '', email: '', phone: '', company: '' });
        clearCart();
      } else {
        // إخفاء toast التحميل وإظهار toast الخطأ
        toast.dismiss(loadingToast);
        toast.error(
          result.message || (locale === 'ar'
            ? 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.'
            : 'An error occurred while sending the request. Please try again.'),
          {
            duration: 6000,
            style: {
              background: '#ef4444',
              color: '#fff',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#ef4444',
            },
          }
        );
      }
    } catch (error) {
      console.error('Error sending quote request:', error);
      // إخفاء toast التحميل وإظهار toast الخطأ
      toast.dismiss(loadingToast);
      toast.error(
        locale === 'ar'
          ? 'حدث خطأ في الاتصال. يرجى التحقق من الإنترنت والمحاولة مرة أخرى.'
          : 'Connection error. Please check your internet and try again.',
        {
          duration: 5000,
          style: {
            background: '#ef4444',
            color: '#fff',
          },
          iconTheme: {
            primary: '#fff',
            secondary: '#ef4444',
          },
        }
      );
    }
  };

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {currentContent.title}
            </h1>
            <p className="text-gray-600">
              {currentContent.subtitle}
            </p>
          </div>

          {cartItems.length === 0 ? (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <i className="ri-shopping-cart-2-line text-6xl text-gray-400 mb-4"></i>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">
                  {currentContent.empty}
                </h2>
                <p className="text-gray-600 mb-6">
                  {currentContent.emptyMessage}
                </p>
                <Link 
                  href={`/${locale}/products`}
                  className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors inline-flex items-center gap-2"
                >
                  <i className="ri-arrow-right-line"></i>
                  {currentContent.browseProducts}
                </Link>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* قائمة المنتجات */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                  <div className="p-6 border-b border-gray-200">
                    <h2 className="text-xl font-bold text-gray-800">
                      {currentContent.product} ({cartItems.length})
                    </h2>
                  </div>
                  
                  <div className="divide-y divide-gray-200">
                    {cartItems.map((item) => (
                      <div key={item.id} className="p-6 flex items-center gap-4">
                        <img
                          src={item.image}
                          alt={locale === 'ar' ? item.titleAr : item.title}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                        
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-800 mb-1">
                            {locale === 'ar' ? item.titleAr : item.title}
                          </h3>
                          <p className="text-primary font-bold">
                            {item.price.toLocaleString()} {currentContent.currency}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-3">
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors"
                          >
                            <i className="ri-subtract-line"></i>
                          </button>
                          
                          <span className="w-12 text-center font-semibold">
                            {item.quantity}
                          </span>
                          
                          <button
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors"
                          >
                            <i className="ri-add-line"></i>
                          </button>
                        </div>
                        
                        <div className="text-right">
                          <p className="font-bold text-gray-800 mb-2">
                            {(item.price * item.quantity).toLocaleString()} {currentContent.currency}
                          </p>
                          <button
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <i className="ri-delete-bin-line"></i>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* ملخص الطلب */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8">
                  <h2 className="text-xl font-bold text-gray-800 mb-6">
                    {currentContent.total}
                  </h2>
                  
                  <div className="space-y-4 mb-6">
                    <div className="flex justify-between items-center py-2 border-b border-gray-200">
                      <span className="text-gray-600">
                        {currentContent.total}
                      </span>
                      <span className="font-bold text-xl text-primary">
                        {getTotalPrice().toLocaleString()} {currentContent.currency}
                      </span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => setShowForm(true)}
                    className="w-full bg-primary text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                  >
                    <i className="ri-file-text-line"></i>
                    {currentContent.requestQuote}
                  </button>
                  
                  <button
                    onClick={clearCart}
                    className="w-full mt-3 bg-gray-200 text-gray-700 py-2 px-6 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                  >
                    {currentContent.remove}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* نموذج طلب عرض السعر */}
          {showForm && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-xl p-6 w-full max-w-md">
                <h3 className="text-xl font-bold text-gray-800 mb-4">
                  {currentContent.customerInfo}
                </h3>
                
                <form onSubmit={handleSend} className="space-y-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      {currentContent.name} *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={form.name}
                      onChange={handleFormChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      {currentContent.email} *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={form.email}
                      onChange={handleFormChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      {currentContent.phone} *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={form.phone}
                      onChange={handleFormChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      {currentContent.company}
                    </label>
                    <input
                      type="text"
                      name="company"
                      value={form.company}
                      onChange={handleFormChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-primary text-white py-2 px-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
                    >
                      {currentContent.send}
                    </button>
                    <button
                      type="button"
                      onClick={() => setShowForm(false)}
                      className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                    >
                      {currentContent.cancel}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
