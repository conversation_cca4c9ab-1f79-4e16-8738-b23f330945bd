import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getAllQuoteRequests,
  getQuoteRequestStats
} from '../../lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

// دالة إرسال الإيميل
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // قراءة إعدادات الإيميل من إعدادات الموقع
    let adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';

    // محاولة قراءة الإيميل من إعدادات التواصل
    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        if (siteSettings.communicationSettings?.email?.adminEmail) {
          adminEmail = siteSettings.communicationSettings.email.adminEmail;
        }
      }
    } catch (error) {
      console.log('Could not read site settings, using default email');
    }

    let emailSettings = {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpSecure: false,
      smtpUser: process.env.EMAIL_USER || '',
      smtpPass: process.env.EMAIL_PASS || '',
      adminEmail: adminEmail,
      fromName: 'Droob Hajer',
      fromNameAr: 'دروب هاجر',
      enabled: true
    };

    // محاولة قراءة إعدادات الإيميل من إعدادات الموقع
    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        if (siteSettings.communicationSettings?.email) {
          const emailConfig = siteSettings.communicationSettings.email;
          emailSettings = {
            smtpHost: emailConfig.smtpHost || emailSettings.smtpHost,
            smtpPort: emailConfig.smtpPort || emailSettings.smtpPort,
            smtpSecure: emailConfig.smtpSecure || emailSettings.smtpSecure,
            smtpUser: emailConfig.smtpUser || emailSettings.smtpUser,
            smtpPass: emailConfig.smtpPass || emailSettings.smtpPass,
            adminEmail: emailConfig.adminEmail || emailSettings.adminEmail,
            fromName: emailConfig.fromName || emailSettings.fromName,
            fromNameAr: emailConfig.fromNameAr || emailSettings.fromNameAr,
            enabled: emailConfig.enabled !== undefined ? emailConfig.enabled : emailSettings.enabled
          };
        }
      }
    } catch (error) {
      console.log('Could not read site settings for email, using defaults');
    }

    // التحقق من تفعيل إرسال الإيميل
    if (!emailSettings.enabled || !emailSettings.smtpUser || !emailSettings.smtpPass) {
      console.log('Email sending is disabled or not configured');
      return { success: false, error: 'Email not configured' };
    }

    // إعدادات SMTP
    const transporter = nodemailer.createTransport({
      host: emailSettings.smtpHost,
      port: emailSettings.smtpPort,
      secure: emailSettings.smtpSecure,
      auth: {
        user: emailSettings.smtpUser,
        pass: emailSettings.smtpPass
      }
    });

    // قراءة ملف الإكسل
    const excelBuffer = fs.readFileSync(excelFilePath);

    // إعداد الإيميل
    const mailOptions = {
      from: `"${emailSettings.fromNameAr}" <${emailSettings.smtpUser}>`,
      to: emailSettings.adminEmail,
      subject: `طلب تسعير جديد - ${quoteRequest.id}`,
      html: `
        <div dir="rtl" style="font-family: Arial, sans-serif;">
          <h2>طلب تسعير جديد</h2>
          <p><strong>رقم الطلب:</strong> ${quoteRequest.id}</p>
          <p><strong>تاريخ الطلب:</strong> ${new Date(quoteRequest.createdAt).toLocaleDateString('ar-SA')}</p>

          <h3>بيانات العميل:</h3>
          <ul>
            <li><strong>الاسم:</strong> ${quoteRequest.customerInfo.name}</li>
            <li><strong>البريد الإلكتروني:</strong> ${quoteRequest.customerInfo.email}</li>
            <li><strong>رقم الهاتف:</strong> ${quoteRequest.customerInfo.phone}</li>
            <li><strong>الشركة:</strong> ${quoteRequest.customerInfo.company}</li>
          </ul>

          <h3>المنتجات المطلوبة:</h3>
          <ul>
            ${quoteRequest.products.map(product =>
              `<li>${product.titleAr || product.title} - الكمية: ${product.quantity}</li>`
            ).join('')}
          </ul>

          <p><strong>إجمالي عدد المنتجات:</strong> ${quoteRequest.products.length}</p>
          <p><strong>إجمالي الكمية:</strong> ${quoteRequest.products.reduce((total, item) => total + item.quantity, 0)}</p>

          <p>يرجى مراجعة ملف الإكسل المرفق لتفاصيل أكثر.</p>
        </div>
      `,
      attachments: [
        {
          filename: `quote-request-${quoteRequest.id}.xlsx`,
          content: excelBuffer,
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      ]
    };

    // إرسال الإيميل
    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// دالة إرسال إشعار بسيط (بدون SMTP معقد)
async function sendSimpleEmailNotification(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // قراءة الإيميل المستقبل من إعدادات الموقع
    let recipientEmail = '<EMAIL>';
    let emailEnabled = false;

    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        if (siteSettings.communicationSettings?.email?.adminEmail) {
          recipientEmail = siteSettings.communicationSettings.email.adminEmail;
        }
        emailEnabled = siteSettings.communicationSettings?.email?.enabled || false;
      }
    } catch (error) {
      console.log('Could not read email settings');
    }

    if (!emailEnabled) {
      console.log('📧 إرسال الإيميل معطل في الإعدادات');
      return { success: false, error: 'Email sending is disabled' };
    }

    // طباعة تفاصيل الإشعار (يمكن استبدالها بخدمة إرسال إيميل حقيقية)
    console.log('📧 ===== إشعار طلب تسعير جديد =====');
    console.log(`📨 المرسل: ${quoteRequest.customerInfo.name} (${quoteRequest.customerInfo.email})`);
    console.log(`📬 المستقبل: ${recipientEmail}`);
    console.log(`🆔 رقم الطلب: ${quoteRequest.id}`);
    console.log(`🏢 الشركة: ${quoteRequest.customerInfo.company || 'غير محدد'}`);
    console.log(`📱 الهاتف: ${quoteRequest.customerInfo.phone}`);
    console.log(`📦 عدد المنتجات: ${quoteRequest.products.length}`);
    console.log(`📄 ملف Excel: ${path.basename(excelFilePath)}`);
    console.log('=====================================');

    // هنا يمكنك إضافة كود إرسال إيميل حقيقي باستخدام:
    // - EmailJS (للإرسال من المتصفح)
    // - Formspree (خدمة مجانية)
    // - SendGrid API (خدمة احترافية)
    // - أو أي خدمة إرسال إيميل أخرى

    return {
      success: true,
      messageId: `notification-${Date.now()}`,
      message: `تم إرسال إشعار طلب التسعير إلى ${recipientEmail}`
    };

  } catch (error) {
    console.error('Error sending email notification:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // التحقق من صحة البيانات
      if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
        });
      }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: null
      };

      const savedQuoteRequest = await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل (نظام بسيط)
      const emailResult = await sendSimpleEmailNotification(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getAllQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map(request => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

      res.status(200).json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
