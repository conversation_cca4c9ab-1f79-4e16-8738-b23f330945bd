import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getAllQuoteRequests,
  getQuoteRequestStats
} from '../../lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}



// دالة إرسال الإيميل مع دعم Hostinger و Gmail
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // قراءة إعدادات الإيميل من إعدادات الموقع
    let emailSettings = {
      smtpHost: 'smtp.hostinger.com',
      smtpPort: 587,
      smtpSecure: false,
      smtpUser: '',
      smtpPass: '',
      adminEmail: '<EMAIL>',
      fromName: 'Droob Hajer',
      fromNameAr: 'دروب هاجر',
      enabled: false
    };

    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        if (siteSettings.communicationSettings?.email) {
          const emailConfig = siteSettings.communicationSettings.email;
          emailSettings = {
            smtpHost: emailConfig.smtpHost || emailSettings.smtpHost,
            smtpPort: emailConfig.smtpPort || emailSettings.smtpPort,
            smtpSecure: emailConfig.smtpSecure || emailSettings.smtpSecure,
            smtpUser: emailConfig.smtpUser || emailSettings.smtpUser,
            smtpPass: emailConfig.smtpPass || emailSettings.smtpPass,
            adminEmail: emailConfig.adminEmail || emailSettings.adminEmail,
            fromName: emailConfig.fromName || emailSettings.fromName,
            fromNameAr: emailConfig.fromNameAr || emailSettings.fromNameAr,
            enabled: emailConfig.enabled !== undefined ? emailConfig.enabled : emailSettings.enabled
          };
        }
      }
    } catch (error) {
      console.log('Could not read site settings for email, using defaults');
    }

    // التحقق من تفعيل إرسال الإيميل
    if (!emailSettings.enabled) {
      console.log('📧 إرسال الإيميل معطل في الإعدادات');
      return { success: false, error: 'Email sending is disabled' };
    }

    if (!emailSettings.smtpUser || !emailSettings.smtpPass) {
      console.log('📧 إعدادات SMTP غير مكتملة');
      return { success: false, error: 'SMTP settings incomplete' };
    }

    // إعدادات SMTP
    const transporter = nodemailer.createTransporter({
      host: emailSettings.smtpHost,
      port: emailSettings.smtpPort,
      secure: emailSettings.smtpSecure, // true for 465, false for other ports
      auth: {
        user: emailSettings.smtpUser,
        pass: emailSettings.smtpPass
      },
      tls: {
        rejectUnauthorized: false // للتوافق مع بعض خوادم SMTP
      }
    });

    // محتوى الإيميل
    const emailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb; text-align: center;">طلب تسعير جديد</h2>

        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0;">معلومات العميل:</h3>
          <p><strong>الاسم:</strong> ${quoteRequest.customerInfo.name}</p>
          <p><strong>الإيميل:</strong> ${quoteRequest.customerInfo.email}</p>
          <p><strong>الهاتف:</strong> ${quoteRequest.customerInfo.phone}</p>
          ${quoteRequest.customerInfo.company ? `<p><strong>الشركة:</strong> ${quoteRequest.customerInfo.company}</p>` : ''}
        </div>

        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0;">تفاصيل الطلب:</h3>
          <p><strong>رقم الطلب:</strong> ${quoteRequest.id}</p>
          <p><strong>عدد المنتجات:</strong> ${quoteRequest.products.length}</p>
          <p><strong>المجموع الكلي:</strong> ${quoteRequest.totalAmount} ريال</p>
          <p><strong>تاريخ الطلب:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>

        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0;">المنتجات المطلوبة:</h3>
          <ul style="list-style: none; padding: 0;">
            ${quoteRequest.products.map(product => `
              <li style="padding: 10px; border-bottom: 1px solid #e5e7eb;">
                <strong>${product.titleAr || product.title}</strong><br>
                الكمية: ${product.quantity} | السعر: ${product.price} ريال
              </li>
            `).join('')}
          </ul>
        </div>

        <p style="text-align: center; color: #6b7280; font-size: 14px;">
          تم إرسال هذا الطلب من موقع ${emailSettings.fromNameAr}
        </p>
      </div>
    `;

    // إعدادات الرسالة
    const mailOptions = {
      from: `"${emailSettings.fromNameAr}" <${emailSettings.smtpUser}>`,
      to: emailSettings.adminEmail,
      replyTo: quoteRequest.customerInfo.email,
      subject: `طلب تسعير جديد - ${quoteRequest.id}`,
      html: emailContent,
      attachments: [
        {
          filename: `${quoteRequest.id}.xlsx`,
          path: excelFilePath
        }
      ]
    };

    // إرسال الإيميل
    const info = await transporter.sendMail(mailOptions);

    console.log('✅ تم إرسال الإيميل بنجاح:', info.messageId);
    return {
      success: true,
      messageId: info.messageId,
      message: `تم إرسال طلب التسعير إلى ${emailSettings.adminEmail}`
    };

  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // التحقق من صحة البيانات
      if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
        });
      }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: null
      };

      const savedQuoteRequest = await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getAllQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map(request => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

      res.status(200).json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
