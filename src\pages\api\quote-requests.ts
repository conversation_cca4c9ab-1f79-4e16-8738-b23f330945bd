import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getAllQuoteRequests,
  getQuoteRequestStats
} from '../../lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}



// دالة إرسال إشعار بسيط (محاكاة إرسال من إيميل العميل)
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // قراءة الإيميل المستقبل من إعدادات الموقع
    let recipientEmail = '<EMAIL>';
    let emailEnabled = false;

    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        if (siteSettings.communicationSettings?.email?.adminEmail) {
          recipientEmail = siteSettings.communicationSettings.email.adminEmail;
        }
        emailEnabled = siteSettings.communicationSettings?.email?.enabled || false;
      }
    } catch (error) {
      console.log('Could not read email settings');
    }

    if (!emailEnabled) {
      console.log('📧 إرسال الإيميل معطل في الإعدادات');
      return { success: false, error: 'Email sending is disabled' };
    }

    // محاكاة إرسال إيميل من العميل إليك
    console.log('📧 ===== محاكاة إرسال إيميل =====');
    console.log(`📨 من: ${quoteRequest.customerInfo.name} <${quoteRequest.customerInfo.email}>`);
    console.log(`📬 إلى: ${recipientEmail}`);
    console.log(`📋 الموضوع: طلب تسعير جديد - ${quoteRequest.id}`);
    console.log(`🏢 الشركة: ${quoteRequest.customerInfo.company || 'غير محدد'}`);
    console.log(`📱 الهاتف: ${quoteRequest.customerInfo.phone}`);
    console.log(`📦 عدد المنتجات: ${quoteRequest.products.length}`);
    console.log(`💰 المجموع: ${quoteRequest.totalAmount} ريال`);
    console.log(`📄 ملف Excel: ${path.basename(excelFilePath)}`);
    console.log('=====================================');

    // هنا يمكنك إضافة خدمة إرسال إيميل حقيقية مثل:
    // - EmailJS (يرسل من المتصفح مباشرة)
    // - Formspree (خدمة مجانية)
    // - SendGrid API (خدمة احترافية)
    // - Mailgun API (خدمة احترافية)
    // - أو استخدام خدمة إرسال إيميل من استضافة Hostinger

    // في المستقبل، يمكن إضافة كود إرسال إيميل حقيقي هنا
    // مثال لمحتوى الإيميل الذي سيتم إرساله:
    const emailContent = `
      الموضوع: طلب تسعير جديد - ${quoteRequest.id}

      من: ${quoteRequest.customerInfo.name} <${quoteRequest.customerInfo.email}>
      إلى: ${recipientEmail}

      مرحباً،

      أرسل لك طلب تسعير جديد:

      معلومات العميل:
      - الاسم: ${quoteRequest.customerInfo.name}
      - الإيميل: ${quoteRequest.customerInfo.email}
      - الهاتف: ${quoteRequest.customerInfo.phone}
      ${quoteRequest.customerInfo.company ? `- الشركة: ${quoteRequest.customerInfo.company}` : ''}

      المنتجات المطلوبة:
      ${quoteRequest.products.map(product =>
        `- ${product.titleAr || product.title} (الكمية: ${product.quantity})`
      ).join('\n')}

      المجموع الكلي: ${quoteRequest.totalAmount} ريال

      ملف Excel مرفق مع التفاصيل الكاملة.

      مع تحياتي،
      ${quoteRequest.customerInfo.name}
    `;

    console.log('📄 محتوى الإيميل المقترح:');
    console.log(emailContent);

    return {
      success: true,
      messageId: `simulated-${Date.now()}`,
      message: `تم محاكاة إرسال طلب التسعير من ${quoteRequest.customerInfo.email} إلى ${recipientEmail}`
    };

  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // التحقق من صحة البيانات
      if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
        });
      }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: null
      };

      const savedQuoteRequest = await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getAllQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map(request => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

      res.status(200).json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
