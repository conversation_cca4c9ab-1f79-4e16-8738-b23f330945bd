import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import { Resend } from 'resend';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getAllQuoteRequests,
  getQuoteRequestStats
} from '../../lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}



// دالة إرسال الإيميل باستخدام Resend فقط
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // استيراد getContactInfo هنا لتجنب مشاكل الاستيراد
    const { getContactInfo } = await import('../../lib/mysql-database');

    // جلب إعدادات الإيميل من قاعدة البيانات
    const contactInfo = await getContactInfo();

    if (!contactInfo?.email || !contactInfo?.Password) {
      console.log('📧 إعدادات الإيميل غير مكتملة في قاعدة البيانات');
      return { success: false, error: 'Email settings not configured in database' };
    }

    // التحقق من تفعيل الإيميل من إعدادات الموقع
    let emailEnabled = false;

    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        emailEnabled = siteSettings.communicationSettings?.email?.enabled || false;
      }
    } catch {
      console.log('Could not read site settings, using defaults');
    }

    if (!emailEnabled) {
      console.log('📧 إرسال الإيميل معطل في الإعدادات');
      return { success: false, error: 'Email sending is disabled' };
    }

    // قراءة ملف Excel وتحويله إلى base64 للـ Resend
    const excelBuffer = fs.readFileSync(excelFilePath);
    const excelBase64 = excelBuffer.toString('base64');

    // إرسال الإيميل باستخدام Resend (من نفس الإيميل إلى نفس الإيميل)
    return await sendWithResend(quoteRequest, contactInfo, contactInfo.email, excelBase64);

  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// دالة إرسال الإيميل باستخدام Resend (من نفس الإيميل إلى نفس الإيميل)
async function sendWithResend(quoteRequest: QuoteRequest, contactInfo: any, recipientEmail: string, excelBase64: string) {
  try {
    const resend = new Resend(contactInfo.Password); // API Key من عمود Password

    console.log('🔄 محاولة إرسال إيميل عبر Resend...');
    console.log('📧 من:', contactInfo.email);
    console.log('📬 إلى:', recipientEmail);
    console.log('🔑 API Key:', contactInfo.Password);

    // محتوى بسيط للإيميل
    const emailContent = `
      <div dir="rtl" style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>طلب تسعير جديد - ${quoteRequest.customerInfo.company}</h2>
        <p><strong>العميل:</strong> ${quoteRequest.customerInfo.name}</p>
        <p><strong>الهاتف:</strong> ${quoteRequest.customerInfo.phone}</p>
        <p><strong>الإيميل:</strong> ${quoteRequest.customerInfo.email}</p>
        ${quoteRequest.customerInfo.company ? `<p><strong>الشركة:</strong> ${quoteRequest.customerInfo.company}</p>` : ''}
        <p><strong>عدد المنتجات:</strong> ${quoteRequest.products.length}</p>
        <p><strong>المجموع:</strong> ${quoteRequest.totalAmount} ريال</p>
        <p>ملف Excel مرفق مع التفاصيل الكاملة.</p>
      </div>
    `;

    const { data, error } = await resend.emails.send({
      from: 'DROOB HAJER<<EMAIL>>',
      to: [recipientEmail],
      replyTo: quoteRequest.customerInfo.email,
      subject: `طلب تسعير جديد من ${quoteRequest.customerInfo.company || quoteRequest.customerInfo.name} `,
      html: emailContent,
      attachments: [
        {
          filename: `طلب-تسعير-${quoteRequest.id}.xlsx`,
          content: excelBase64,
          contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      ]
    });

    if (error) {
      console.error('❌ خطأ من Resend API:', error);
      return { success: false, error: error.message || 'Resend API error' };
    }

    console.log('✅ تم إرسال الإيميل بنجاح عبر Resend!');
    console.log('🆔 Message ID:', data?.id);
    console.log('📊 استجابة Resend:', JSON.stringify(data, null, 2));

    return {
      success: true,
      messageId: data?.id,
      message: 'تم إرسال طلب التسعير بنجاح'
    };

  } catch (error) {
    console.error('❌ خطأ في إرسال الإيميل عبر Resend:');
    console.error('📧 الإيميل:', contactInfo.email);
    console.error('🔑 API Key:', contactInfo.Password);
    console.error('📄 تفاصيل الخطأ:', error);

    return { success: false, error: error instanceof Error ? error.message : 'Resend API error' };
  }
}





export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // التحقق من صحة البيانات
      if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
        });
      }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: undefined
      };

      await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: emailResult.success
          ? 'تم إرسال طلب التسعير بنجاح! ستصلك رسالة تأكيد قريباً.'
          : 'تم حفظ طلب التسعير بنجاح، لكن لم يتم إرسال الإيميل.',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success,
        emailError: emailResult.success ? null : emailResult.error
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'عذراً، حدث خطأ تقني أثناء معالجة طلبك. يرجى المحاولة مرة أخرى أو التواصل معنا مباشرة.',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getAllQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map(request => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

      res.status(200).json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'عذراً، حدث خطأ أثناء تحميل البيانات. يرجى إعادة تحميل الصفحة.',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
