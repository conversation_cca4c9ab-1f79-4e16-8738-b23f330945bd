import { NextApiRequest, NextApiResponse } from 'next';
import * as XLSX from 'xlsx';
import fs from 'fs';
import path from 'path';
import { Resend } from 'resend';
import {
  addQuoteRequest,
  addQuoteRequestProduct,
  getAllQuoteRequests,
  getQuoteRequestStats
} from '../../lib/mysql-database';

interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}



// دالة إرسال الإيميل باستخدام Resend فقط
async function sendEmailWithExcel(quoteRequest: QuoteRequest, excelFilePath: string) {
  try {
    // استيراد getContactInfo هنا لتجنب مشاكل الاستيراد
    const { getContactInfo } = await import('../../lib/mysql-database');

    // جلب إعدادات الإيميل من قاعدة البيانات
    const contactInfo = await getContactInfo();

    if (!contactInfo?.email || !contactInfo?.Password) {
      console.log('📧 إعدادات الإيميل غير مكتملة في قاعدة البيانات');
      return { success: false, error: 'Email settings not configured in database' };
    }

    // التحقق من تفعيل الإيميل من إعدادات الموقع
    let emailEnabled = false;

    try {
      const settingsPath = path.join(process.cwd(), 'src', 'data', 'site-settings.json');
      if (fs.existsSync(settingsPath)) {
        const siteSettings = JSON.parse(fs.readFileSync(settingsPath, 'utf-8'));
        emailEnabled = siteSettings.communicationSettings?.email?.enabled || false;
      }
    } catch {
      console.log('Could not read site settings, using defaults');
    }

    if (!emailEnabled) {
      console.log('📧 إرسال الإيميل معطل في الإعدادات');
      return { success: false, error: 'Email sending is disabled' };
    }

    // قراءة ملف Excel وتحويله إلى base64 للـ Resend
    const excelBuffer = fs.readFileSync(excelFilePath);
    const excelBase64 = excelBuffer.toString('base64');

    // إرسال الإيميل باستخدام Resend (من نفس الإيميل إلى نفس الإيميل)
    return await sendWithResend(quoteRequest, contactInfo, contactInfo.email, excelBase64);

  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// دالة إرسال الإيميل باستخدام Resend (من نفس الإيميل إلى نفس الإيميل)
async function sendWithResend(quoteRequest: QuoteRequest, contactInfo: any, recipientEmail: string, excelBase64: string) {
  try {
    const resend = new Resend(contactInfo.Password); // API Key من عمود Password

    // محتوى الإيميل بتنسيق HTML جميل مع بيانات العميل
    const emailContent = createEmailHTML(quoteRequest);

    const result = await resend.emails.send({
      from: `"دروب هاجر - طلبات التسعير" <${contactInfo.email}>`, // المرسل: نفس الإيميل
      to: recipientEmail, // المستقبل: نفس الإيميل
      replyTo: quoteRequest.customerInfo.email, // الرد إلى العميل
      subject: `طلب تسعير جديد من ${quoteRequest.customerInfo.company || quoteRequest.customerInfo.name} - ${quoteRequest.id}`,
      html: emailContent,
      attachments: [
        {
          filename: `طلب-تسعير-${quoteRequest.id}.xlsx`,
          content: excelBase64,
          content_type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        }
      ]
    });

    console.log('✅ تم إرسال الإيميل بنجاح عبر Resend:', result.data?.id);
    console.log(`📨 المرسل: ${contactInfo.email}`);
    console.log(`📬 المستقبل: ${recipientEmail} (نفس الإيميل)`);
    console.log(`🔄 الرد إلى: ${quoteRequest.customerInfo.name} <${quoteRequest.customerInfo.email}>`);
    console.log(`📋 بيانات العميل: ${quoteRequest.customerInfo.name} - ${quoteRequest.customerInfo.phone} - ${quoteRequest.customerInfo.company || 'بدون شركة'}`);

    return {
      success: true,
      messageId: result.data?.id,
      message: `تم إرسال طلب التسعير بنجاح من ${contactInfo.email} إلى نفس الإيميل مع بيانات العميل وملف Excel`
    };

  } catch (error) {
    console.error('Error sending email with Resend:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Resend API error' };
  }
}



// دالة إنشاء محتوى HTML للإيميل
function createEmailHTML(quoteRequest: QuoteRequest): string {
  return `
    <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #f9fafb; padding: 20px;">
      <div style="background: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0; font-size: 28px;">طلب تسعير جديد</h1>
          <p style="color: #6b7280; margin: 10px 0 0 0;">رقم الطلب: ${quoteRequest.id}</p>
        </div>

        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0; display: flex; align-items: center;">
            <span style="background: #3b82f6; color: white; width: 24px; height: 24px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-left: 10px; font-size: 14px;">👤</span>
            معلومات العميل
          </h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">الاسم:</td><td style="padding: 8px 0; color: #6b7280;">${quoteRequest.customerInfo.name}</td></tr>
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">الإيميل:</td><td style="padding: 8px 0; color: #6b7280;">${quoteRequest.customerInfo.email}</td></tr>
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">الهاتف:</td><td style="padding: 8px 0; color: #6b7280;">${quoteRequest.customerInfo.phone}</td></tr>
            ${quoteRequest.customerInfo.company ? `<tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">الشركة:</td><td style="padding: 8px 0; color: #6b7280;">${quoteRequest.customerInfo.company}</td></tr>` : ''}
          </table>
        </div>

        <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0; display: flex; align-items: center;">
            <span style="background: #06b6d4; color: white; width: 24px; height: 24px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-left: 10px; font-size: 14px;">📋</span>
            تفاصيل الطلب
          </h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">عدد المنتجات:</td><td style="padding: 8px 0; color: #6b7280;">${quoteRequest.products.length}</td></tr>
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">المجموع الكلي:</td><td style="padding: 8px 0; color: #059669; font-weight: bold;">${quoteRequest.totalAmount} ريال</td></tr>
            <tr><td style="padding: 8px 0; font-weight: bold; color: #374151;">تاريخ الطلب:</td><td style="padding: 8px 0; color: #6b7280;">${new Date().toLocaleDateString('ar-SA')}</td></tr>
          </table>
        </div>

        <div style="background: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #374151; margin-top: 0; display: flex; align-items: center;">
            <span style="background: #10b981; color: white; width: 24px; height: 24px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-left: 10px; font-size: 14px;">📦</span>
            المنتجات المطلوبة
          </h3>
          <div style="background: white; border-radius: 6px; overflow: hidden;">
            ${quoteRequest.products.map((product, index) => `
              <div style="padding: 15px; border-bottom: ${index < quoteRequest.products.length - 1 ? '1px solid #e5e7eb' : 'none'};">
                <div style="font-weight: bold; color: #374151; margin-bottom: 5px;">${product.titleAr || product.title}</div>
                <div style="color: #6b7280; font-size: 14px;">
                  الكمية: <span style="font-weight: bold; color: #059669;">${product.quantity}</span> |
                  السعر: <span style="font-weight: bold; color: #059669;">${product.price} ريال</span>
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center;">
          <p style="margin: 0; color: #92400e; font-weight: bold;">📎 ملف Excel مرفق مع التفاصيل الكاملة</p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px; margin: 0;">
            تم إرسال هذا الطلب من موقع دروب هاجر<br>
            للرد على العميل، اضغط على "رد" في برنامج البريد الإلكتروني
          </p>
        </div>
      </div>
    </div>
  `;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const { customerInfo, products } = req.body;

      // التحقق من صحة البيانات
      if (!customerInfo || !products || !Array.isArray(products) || products.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'بيانات غير صحيحة: يجب إرسال معلومات العميل والمنتجات'
        });
      }

      // إنشاء معرف فريد للطلب
      const requestId = `QR-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      // حساب المجموع الكلي
      const totalAmount = products.reduce((total: number, item: CartItem) =>
        total + (item.price * item.quantity), 0
      );

      // إنشاء مجلد uploads/excel إذا لم يكن موجوداً
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
      const excelDir = path.join(uploadsDir, 'excel');

      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // إنشاء ملف Excel للمنتجات
      const excelFileName = `${requestId}.xlsx`;
      const excelFilePath = path.join(excelDir, excelFileName);
      
      // تحضير البيانات لملف Excel
      const excelData = products.map((product: CartItem, index: number) => ({
        'رقم المنتج': index + 1,
        'اسم المنتج': product.titleAr || product.title,
        'الكمية': product.quantity,
        'السعر المقترح': '', // سيتم ملؤه من قبل الإدارة
        'ملاحظات': ''
      }));

      // إنشاء workbook وworksheet
      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      
      // تحسين عرض الأعمدة
      const columnWidths = [
        { wch: 10 }, // رقم المنتج
        { wch: 30 }, // اسم المنتج
        { wch: 10 }, // الكمية
        { wch: 15 }, // السعر المقترح
        { wch: 20 }  // ملاحظات
      ];
      worksheet['!cols'] = columnWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, 'طلب التسعير');
      XLSX.writeFile(workbook, excelFilePath);

      // حفظ طلب التسعير في قاعدة البيانات
      const quoteRequestData = {
        id: requestId,
        customer_name: customerInfo.name,
        customer_email: customerInfo.email,
        customer_phone: customerInfo.phone,
        customer_company: customerInfo.company || null,
        excel_file_url: `uploads/excel/${excelFileName}`,
        status: 'pending' as const,
        notes: undefined
      };

      const savedQuoteRequest = await addQuoteRequest(quoteRequestData);

      // حفظ المنتجات المرتبطة بطلب التسعير
      for (const product of products) {
        await addQuoteRequestProduct(requestId, product.id);
      }

      // إنشاء كائن طلب التسعير للإيميل (بالتنسيق القديم للتوافق)
      const quoteRequest: QuoteRequest = {
        id: requestId,
        customerInfo,
        products,
        totalAmount,
        createdAt: new Date().toISOString(),
        status: 'pending',
        excelFilePath: `src/data/excel/${excelFileName}`
      };

      // إرسال الإيميل مع ملف الإكسل
      const emailResult = await sendEmailWithExcel(quoteRequest, excelFilePath);

      if (emailResult.success) {
        console.log('Email sent successfully for quote request:', requestId);
      } else {
        console.error('Failed to send email for quote request:', requestId, emailResult.error);
      }

      res.status(200).json({
        success: true,
        message: 'تم إرسال طلب التسعير بنجاح',
        requestId,
        excelFilePath: quoteRequest.excelFilePath,
        emailSent: emailResult.success
      });

    } catch (error) {
      console.error('Error creating quote request:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء معالجة الطلب'
      });
    }
  } else if (req.method === 'GET') {
    try {
      // جلب جميع طلبات التسعير من قاعدة البيانات
      const dbRequests = await getAllQuoteRequests();

      // تحويل البيانات إلى التنسيق المتوقع من الواجهة الأمامية
      const formattedRequests = dbRequests.map(request => ({
        id: request.id,
        customerInfo: {
          name: request.customer_name,
          email: request.customer_email,
          phone: request.customer_phone,
          company: request.customer_company || ''
        },
        products: [], // سيتم ملؤها لاحقاً إذا لزم الأمر
        totalAmount: 0, // يمكن حسابها لاحقاً إذا لزم الأمر
        createdAt: request.created_at.toISOString(),
        status: request.status,
        excelFilePath: request.excel_file_url || ''
      }));

      res.status(200).json({ success: true, requests: formattedRequests });

    } catch (error) {
      console.error('Error fetching quote requests:', error);
      res.status(500).json({
        success: false,
        message: 'حدث خطأ أثناء جلب الطلبات'
      });
    }
  } else {
    res.setHeader('Allow', ['GET', 'POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
