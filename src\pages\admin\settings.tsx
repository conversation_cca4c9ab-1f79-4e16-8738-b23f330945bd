import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import SettingsStatus from '../../components/admin/SettingsStatus';
import HeroImageUpload from '../../components/admin/HeroImageUpload';
import { getSiteSettings, saveSiteSettings } from '../../data/settings';
import { SiteSettings } from '../../types/admin';


// مكون إعدادات SMTP
const SMTPSettingsComponent = () => {
  const [smtpSettings, setSmtpSettings] = useState({
    email: '',
    password: '',
    hasPassword: false
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // جلب الإعدادات الحالية
  useEffect(() => {
    fetchSMTPSettings();
  }, []);

  const fetchSMTPSettings = async () => {
    try {
      const response = await fetch('/api/smtp-settings');
      const data = await response.json();
      if (data.success) {
        setSmtpSettings({
          email: data.settings.email,
          password: '',
          hasPassword: data.settings.hasPassword
        });
      }
    } catch (error) {
      console.error('Error fetching SMTP settings:', error);
    }
  };

  const handleSave = async () => {
    if (!smtpSettings.email || !smtpSettings.password) {
      alert('يرجى إدخال الإيميل وكلمة المرور');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/smtp-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: smtpSettings.email,
          password: smtpSettings.password
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('تم حفظ إعدادات SMTP بنجاح');
        setSmtpSettings(prev => ({ ...prev, hasPassword: true, password: '' }));
      } else {
        alert(data.error || 'فشل في حفظ الإعدادات');
      }
    } catch (error) {
      alert('حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            إيميل Titan Email
          </label>
          <input
            type="email"
            value={smtpSettings.email}
            onChange={(e) => setSmtpSettings(prev => ({ ...prev, email: e.target.value }))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="<EMAIL>"
          />
          <p className="text-sm text-gray-500 mt-1">
            إيميلك الكامل في Titan Email
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            كلمة مرور الإيميل
          </label>
          <div className="relative">
            <input
              type={showPassword ? 'text' : 'password'}
              value={smtpSettings.password}
              onChange={(e) => setSmtpSettings(prev => ({ ...prev, password: e.target.value }))}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 pl-12"
              placeholder={smtpSettings.hasPassword ? 'أدخل كلمة مرور جديدة' : 'كلمة مرور الإيميل'}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <i className={`ri-${showPassword ? 'eye-off' : 'eye'}-line`}></i>
            </button>
          </div>
          {smtpSettings.hasPassword && (
            <p className="text-sm text-green-600 mt-1">
              ✓ كلمة المرور محفوظة (اتركها فارغة للاحتفاظ بالحالية)
            </p>
          )}
        </div>
      </div>

      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start">
          <i className="ri-settings-line text-orange-500 text-lg ml-2 mt-0.5"></i>
          <div className="text-sm text-orange-700">
            <p className="font-medium mb-2">إعدادات Titan Email التلقائية:</p>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>خادم SMTP:</strong> smtp.titan.email</li>
              <li><strong>المنفذ:</strong> 465 (SSL)</li>
              <li><strong>الأمان:</strong> SSL/TLS مفعل</li>
              <li><strong>المرسل:</strong> إيميلك</li>
              <li><strong>الرد إلى:</strong> إيميل العميل</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={loading}
          className="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
        >
          {loading ? (
            <>
              <i className="ri-loader-4-line animate-spin"></i>
              جاري الحفظ...
            </>
          ) : (
            <>
              <i className="ri-save-line"></i>
              حفظ إعدادات SMTP
            </>
          )}
        </button>
      </div>
    </div>
  );
};

const SettingsAdmin = () => {
  const [settings, setSettings] = useState<SiteSettings>({
    siteName: '',
    siteNameAr: '',
    contactEmail: '',
    whatsappNumber: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      twitter: '',
      linkedin: '',
      youtube: ''
    },
    heroImages: [''],
    aboutText: '',
    aboutTextAr: '',
    address: '',
    addressAr: '',
    phone: '',
    workingHours: '',
    workingHoursAr: '',
    contactSettings: {
      mapSettings: {
        latitude: 24.7136,
        longitude: 46.6753,
        zoom: 15,
        googleMapsUrl: '',
        showMap: true,
      },
      contactFormSettings: {
        enableContactForm: true,
        successMessage: '',
        successMessageAr: '',
        errorMessage: '',
        errorMessageAr: '',
      },
      officeHours: {
        enabled: true,
        hoursText: '',
        hoursTextAr: '',
      },
      additionalInfo: {
        description: '',
        descriptionAr: '',
        showFAQ: true,
      },
    },
    aboutSettings: {
      heroSection: {
        title: '',
        titleAr: '',
        subtitle: '',
        subtitleAr: '',
      },
      description: {
        text: '',
        textAr: '',
      },
      vision: {
        title: '',
        titleAr: '',
        text: '',
        textAr: '',
      },
      mission: {
        title: '',
        titleAr: '',
        text: '',
        textAr: '',
      },
      values: {
        title: '',
        titleAr: '',
        items: []
      },
      team: {
        title: '',
        titleAr: '',
        description: '',
        descriptionAr: '',
      },
      contactCTA: {
        title: '',
        titleAr: '',
        description: '',
        descriptionAr: '',
        enabled: true,
      },
    },
    partnersSettings: {
      title: '',
      titleAr: '',
      description: '',
      descriptionAr: '',
      enabled: true,
      items: []
    },
    headerSettings: {
      logo: '',
      showLanguageSwitch: true,
      showSearchBar: true,
      navigationItems: [
        { nameEn: 'Home', nameAr: 'الرئيسية', url: '/', isActive: true },
        { nameEn: 'Products', nameAr: 'المنتجات', url: '/products', isActive: true },
        { nameEn: 'Categories', nameAr: 'الفئات', url: '/categories', isActive: true },
        { nameEn: 'About', nameAr: 'من نحن', url: '/about', isActive: true },
        { nameEn: 'Contact', nameAr: 'تواصل معنا', url: '/contact', isActive: true }
      ]
    },
    footerSettings: {
      copyrightText: 'All rights reserved',
      copyrightTextAr: 'جميع الحقوق محفوظة',
      showSocialLinks: true,
      quickLinks: [
        { nameEn: 'Privacy Policy', nameAr: 'سياسة الخصوصية', url: '/privacy', isActive: true },
        { nameEn: 'Terms of Service', nameAr: 'شروط الخدمة', url: '/terms', isActive: true },
        { nameEn: 'FAQ', nameAr: 'الأسئلة الشائعة', url: '/faq', isActive: true }
      ],
      companyInfo: {
        description: '',
        descriptionAr: '',
        showAddress: true,
        showPhone: true,
        showEmail: true,
        showWorkingHours: true
      }
    },
    communicationSettings: {
      email: {
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        smtpSecure: false,
        smtpUser: '',
        smtpPass: '',
        adminEmail: '<EMAIL>',
        fromName: 'Droob Hajer',
        fromNameAr: 'دروب هاجر',
        enabled: false
      },
      whatsapp: {
        businessNumber: '+966501234567',
        welcomeMessage: 'Hello! How can we help you today?',
        welcomeMessageAr: 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
        quoteResponseMessage: 'Thank you for your quote request. We will contact you soon with our offer.',
        quoteResponseMessageAr: 'شكراً لك على طلب التسعير. سنتواصل معك قريباً بعرضنا.',
        enabled: true
      }
    }
  });
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');

  // جلب الإعدادات من API
  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
      } else {
        console.error('Failed to fetch settings');
        // استخدام الإعدادات المحلية كبديل
        const currentSettings = getSiteSettings();
        setSettings(currentSettings);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      // استخدام الإعدادات المحلية كبديل
      const currentSettings = getSiteSettings();
      setSettings(currentSettings);
    }
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      // الحصول على التوكن
      const token = localStorage.getItem('authToken') ||
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

      // إرسال البيانات إلى API
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include',
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        const updatedSettings = await response.json();
        setSettings(updatedSettings);

        // حفظ في localStorage أيضاً للتوافق مع النظام الحالي
        saveSiteSettings(updatedSettings);

        setSaveMessage('تم حفظ الإعدادات بنجاح وتحديث الموقع');

        // إطلاق حدث مخصص لإعلام المكونات الأخرى بتحديث الإعدادات
        window.dispatchEvent(new CustomEvent('siteSettingsUpdated'));

        // إعادة تحميل الصفحة بعد 3 ثوان لضمان تطبيق التغييرات
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        const errorData = await response.json();
        setSaveMessage(errorData.messageAr || errorData.message || 'حدث خطأ أثناء حفظ الإعدادات');
      }

      // إخفاء الرسالة بعد 5 ثوان
      setTimeout(() => setSaveMessage(''), 5000);
    } catch (error) {
      console.error('Save settings error:', error);
      setSaveMessage('حدث خطأ في الاتصال بالخادم');
      setTimeout(() => setSaveMessage(''), 5000);
    } finally {
      setIsSaving(false);
    }
  };



  const updateCommunicationSettings = (type: 'email' | 'whatsapp', updates: any) => {
    setSettings(prev => ({
      ...prev,
      communicationSettings: {
        email: {
          smtpHost: prev.communicationSettings?.email?.smtpHost || 'smtp.gmail.com',
          smtpPort: prev.communicationSettings?.email?.smtpPort || 587,
          smtpSecure: prev.communicationSettings?.email?.smtpSecure || false,
          smtpUser: prev.communicationSettings?.email?.smtpUser || '',
          smtpPass: prev.communicationSettings?.email?.smtpPass || '',
          adminEmail: prev.communicationSettings?.email?.adminEmail || '<EMAIL>',
          fromName: prev.communicationSettings?.email?.fromName || 'Droob Hajer',
          fromNameAr: prev.communicationSettings?.email?.fromNameAr || 'دروب هاجر',
          enabled: prev.communicationSettings?.email?.enabled || false,
          ...(type === 'email' ? updates : {})
        },
        whatsapp: {
          businessNumber: prev.communicationSettings?.whatsapp?.businessNumber || '+966501234567',
          welcomeMessage: prev.communicationSettings?.whatsapp?.welcomeMessage || 'Hello! How can we help you today?',
          welcomeMessageAr: prev.communicationSettings?.whatsapp?.welcomeMessageAr || 'مرحباً! كيف يمكننا مساعدتك اليوم؟',
          quoteResponseMessage: prev.communicationSettings?.whatsapp?.quoteResponseMessage || 'Thank you for your quote request. We will contact you soon with our offer.',
          quoteResponseMessageAr: prev.communicationSettings?.whatsapp?.quoteResponseMessageAr || 'شكراً لك على طلب التسعير. سنتواصل معك قريباً بعرضنا.',
          enabled: prev.communicationSettings?.whatsapp?.enabled || true,
          ...(type === 'whatsapp' ? updates : {})
        }
      }
    }));
  };

  const tabs = [
    { id: 'general', title: 'عام', icon: 'ri-settings-line' },
    { id: 'header', title: 'الهيدر', icon: 'ri-layout-top-line' },
    { id: 'footer', title: 'الفوتر', icon: 'ri-layout-bottom-line' },
    { id: 'contact', title: 'التواصل', icon: 'ri-phone-line' },
    { id: 'about', title: 'من نحن', icon: 'ri-information-line' },
    { id: 'partners', title: 'الشركاء', icon: 'ri-handshake-line' },
    { id: 'communication', title: 'إعدادات التواصل', icon: 'ri-message-3-line' },
    { id: 'hero', title: 'صور الهيرو', icon: 'ri-image-line' }
  ];

  return (
    <>
      <Head>
        <title>إعدادات الموقع - لوحة التحكم</title>
        <meta name="description" content="إعدادات الموقع العامة" />
      </Head>

      <AdminLayout title="إعدادات الموقع">
        <div className="space-y-6">
          {/* Settings Status */}
          <SettingsStatus onRefresh={fetchSettings} />

          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">إعدادات الموقع</h1>
              <p className="text-gray-600">إدارة إعدادات الموقع العامة</p>
            </div>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 disabled:opacity-50 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                  جاري الحفظ...
                </>
              ) : (
                <>
                  <i className="ri-save-line text-lg ml-2"></i>
                  حفظ الإعدادات
                </>
              )}
            </button>
          </div>

          {/* Save Message */}
          {saveMessage && (
            <div className={`p-4 rounded-lg flex items-center ${
              saveMessage.includes('نجاح') 
                ? 'bg-green-50 border border-green-200 text-green-700' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <i className={`${saveMessage.includes('نجاح') ? 'ri-check-line' : 'ri-error-warning-line'} text-lg ml-2`}></i>
              {saveMessage}
            </div>
          )}

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            {/* Tabs */}
            <div className="border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
              <nav className="flex space-x-8 space-x-reverse px-6">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-3 border-b-2 font-medium text-sm transition-all duration-300 transform hover:scale-105 ${
                      activeTab === tab.id
                        ? 'border-primary text-primary bg-primary/5 rounded-t-lg'
                        : 'border-transparent text-gray-500 hover:text-primary hover:border-primary/30 hover:bg-primary/5 rounded-t-lg'
                    }`}
                  >
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ml-2 ${
                        activeTab === tab.id ? 'bg-primary/20 text-primary' : 'bg-gray-100 text-gray-500'
                      }`}>
                        <i className={`${tab.icon} text-lg`}></i>
                      </div>
                      {tab.title}
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* General Settings */}
              {activeTab === 'general' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">الإعدادات العامة</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الموقع بالعربية
                      </label>
                      <input
                        type="text"
                        value={settings.siteNameAr}
                        onChange={(e) => setSettings({...settings, siteNameAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="اسم الموقع بالعربية"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم الموقع بالإنجليزية
                      </label>
                      <input
                        type="text"
                        value={settings.siteName}
                        onChange={(e) => setSettings({...settings, siteName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Site name in English"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان بالعربية
                      </label>
                      <textarea
                        value={settings.addressAr}
                        onChange={(e) => setSettings({...settings, addressAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="العنوان بالعربية"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        العنوان بالإنجليزية
                      </label>
                      <textarea
                        value={settings.address}
                        onChange={(e) => setSettings({...settings, address: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="Address in English"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ساعات العمل بالعربية
                      </label>
                      <input
                        type="text"
                        value={settings.workingHoursAr}
                        onChange={(e) => setSettings({...settings, workingHoursAr: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="الأحد - الخميس: 8:00 صباحاً - 6:00 مساءً"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        ساعات العمل بالإنجليزية
                      </label>
                      <input
                        type="text"
                        value={settings.workingHours}
                        onChange={(e) => setSettings({...settings, workingHours: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Sunday - Thursday: 8:00 AM - 6:00 PM"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Header Settings */}
              {activeTab === 'header' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات الهيدر</h3>

                  {/* Logo */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      شعار الموقع
                    </label>
                    <input
                      type="url"
                      value={settings.headerSettings.logo || ''}
                      onChange={(e) => setSettings({
                        ...settings,
                        headerSettings: { ...settings.headerSettings, logo: e.target.value }
                      })}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com/logo.png"
                    />
                  </div>

                  {/* Header Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="showLanguageSwitch"
                        checked={settings.headerSettings.showLanguageSwitch}
                        onChange={(e) => setSettings({
                          ...settings,
                          headerSettings: { ...settings.headerSettings, showLanguageSwitch: e.target.checked }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showLanguageSwitch" className="mr-2 block text-sm text-gray-700">
                        إظهار مبدل اللغة
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="showSearchBar"
                        checked={settings.headerSettings.showSearchBar}
                        onChange={(e) => setSettings({
                          ...settings,
                          headerSettings: { ...settings.headerSettings, showSearchBar: e.target.checked }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showSearchBar" className="mr-2 block text-sm text-gray-700">
                        إظهار شريط البحث
                      </label>
                    </div>
                  </div>

                  {/* Navigation Items */}
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">عناصر التنقل</h4>
                    <div className="space-y-4">
                      {settings.headerSettings.navigationItems.map((item, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الاسم بالعربية
                              </label>
                              <input
                                type="text"
                                value={item.nameAr}
                                onChange={(e) => {
                                  const newItems = [...settings.headerSettings.navigationItems];
                                  newItems[index] = { ...item, nameAr: e.target.value };
                                  setSettings({
                                    ...settings,
                                    headerSettings: { ...settings.headerSettings, navigationItems: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="الرئيسية"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الاسم بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={item.nameEn}
                                onChange={(e) => {
                                  const newItems = [...settings.headerSettings.navigationItems];
                                  newItems[index] = { ...item, nameEn: e.target.value };
                                  setSettings({
                                    ...settings,
                                    headerSettings: { ...settings.headerSettings, navigationItems: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Home"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الرابط
                              </label>
                              <input
                                type="text"
                                value={item.url}
                                onChange={(e) => {
                                  const newItems = [...settings.headerSettings.navigationItems];
                                  newItems[index] = { ...item, url: e.target.value };
                                  setSettings({
                                    ...settings,
                                    headerSettings: { ...settings.headerSettings, navigationItems: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="/"
                              />
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id={`nav-active-${index}`}
                                checked={item.isActive}
                                onChange={(e) => {
                                  const newItems = [...settings.headerSettings.navigationItems];
                                  newItems[index] = { ...item, isActive: e.target.checked };
                                  setSettings({
                                    ...settings,
                                    headerSettings: { ...settings.headerSettings, navigationItems: newItems }
                                  });
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`nav-active-${index}`} className="mr-2 block text-sm text-gray-700">
                                نشط
                              </label>
                            </div>

                            {settings.headerSettings.navigationItems.length > 1 && (
                              <button
                                type="button"
                                onClick={() => {
                                  const newItems = settings.headerSettings.navigationItems.filter((_, i) => i !== index);
                                  setSettings({
                                    ...settings,
                                    headerSettings: { ...settings.headerSettings, navigationItems: newItems }
                                  });
                                }}
                                className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                                title="حذف العنصر"
                              >
                                <i className="ri-delete-bin-line text-lg"></i>
                              </button>
                            )}
                          </div>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => {
                          setSettings({
                            ...settings,
                            headerSettings: {
                              ...settings.headerSettings,
                              navigationItems: [
                                ...settings.headerSettings.navigationItems,
                                { nameEn: '', nameAr: '', url: '', isActive: true }
                              ]
                            }
                          });
                        }}
                        className="w-full border-2 border-dashed border-gray-300 hover:border-blue-500 text-gray-600 hover:text-blue-600 py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        <i className="ri-add-line text-lg ml-2"></i>
                        إضافة عنصر تنقل جديد
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Footer Settings */}
              {activeTab === 'footer' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات الفوتر</h3>

                  {/* Copyright */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نص حقوق النشر بالعربية
                      </label>
                      <input
                        type="text"
                        value={settings.footerSettings.copyrightTextAr}
                        onChange={(e) => setSettings({
                          ...settings,
                          footerSettings: { ...settings.footerSettings, copyrightTextAr: e.target.value }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="جميع الحقوق محفوظة"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نص حقوق النشر بالإنجليزية
                      </label>
                      <input
                        type="text"
                        value={settings.footerSettings.copyrightText}
                        onChange={(e) => setSettings({
                          ...settings,
                          footerSettings: { ...settings.footerSettings, copyrightText: e.target.value }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="All rights reserved"
                      />
                    </div>
                  </div>

                  {/* Company Description */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وصف الشركة في الفوتر بالعربية
                      </label>
                      <textarea
                        value={settings.footerSettings.companyInfo.descriptionAr}
                        onChange={(e) => setSettings({
                          ...settings,
                          footerSettings: {
                            ...settings.footerSettings,
                            companyInfo: { ...settings.footerSettings.companyInfo, descriptionAr: e.target.value }
                          }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="وصف مختصر للشركة يظهر في الفوتر"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وصف الشركة في الفوتر بالإنجليزية
                      </label>
                      <textarea
                        value={settings.footerSettings.companyInfo.description}
                        onChange={(e) => setSettings({
                          ...settings,
                          footerSettings: {
                            ...settings.footerSettings,
                            companyInfo: { ...settings.footerSettings.companyInfo, description: e.target.value }
                          }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                        placeholder="Brief company description for footer"
                      />
                    </div>
                  </div>

                  {/* Contact Info Section */}
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">معلومات التواصل (Contact Info)</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الهاتف
                        </label>
                        <input
                          type="tel"
                          value={settings.phone}
                          onChange={(e) => setSettings({...settings, phone: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="+966 11 234 5678"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          البريد الإلكتروني
                        </label>
                        <input
                          type="email"
                          value={settings.contactEmail}
                          onChange={(e) => setSettings({...settings, contactEmail: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان
                        </label>
                        <input
                          type="text"
                          value={settings.addressAr}
                          onChange={(e) => setSettings({...settings, addressAr: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Riyadh, Saudi Arabia"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Follow Us Section */}
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">تابعنا (Follow Us)</h4>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <i className="ri-facebook-fill text-blue-600 ml-2"></i>
                          فيسبوك
                        </label>
                        <input
                          type="url"
                          value={settings.socialLinks.facebook || ''}
                          onChange={(e) => setSettings({
                            ...settings,
                            socialLinks: {...settings.socialLinks, facebook: e.target.value}
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://facebook.com/droobhajer"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <i className="ri-instagram-fill text-pink-600 ml-2"></i>
                          إنستغرام
                        </label>
                        <input
                          type="url"
                          value={settings.socialLinks.instagram || ''}
                          onChange={(e) => setSettings({
                            ...settings,
                            socialLinks: {...settings.socialLinks, instagram: e.target.value}
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://instagram.com/droobhajer"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <i className="ri-twitter-fill text-blue-400 ml-2"></i>
                          تويتر
                        </label>
                        <input
                          type="url"
                          value={settings.socialLinks.twitter || ''}
                          onChange={(e) => setSettings({
                            ...settings,
                            socialLinks: {...settings.socialLinks, twitter: e.target.value}
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://twitter.com/droobhajer"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <i className="ri-linkedin-fill text-blue-700 ml-2"></i>
                          لينكد إن
                        </label>
                        <input
                          type="url"
                          value={settings.socialLinks.linkedin || ''}
                          onChange={(e) => setSettings({
                            ...settings,
                            socialLinks: {...settings.socialLinks, linkedin: e.target.value}
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://linkedin.com/company/droobhajer"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          <i className="ri-youtube-fill text-red-600 ml-2"></i>
                          يوتيوب
                        </label>
                        <input
                          type="url"
                          value={settings.socialLinks.youtube || ''}
                          onChange={(e) => setSettings({
                            ...settings,
                            socialLinks: {...settings.socialLinks, youtube: e.target.value}
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="https://youtube.com/@droobhajer"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Footer Display Options */}
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">خيارات العرض</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="showSocialLinks"
                          checked={settings.footerSettings.showSocialLinks}
                          onChange={(e) => setSettings({
                            ...settings,
                            footerSettings: { ...settings.footerSettings, showSocialLinks: e.target.checked }
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="showSocialLinks" className="mr-2 block text-sm text-gray-700">
                          إظهار Follow Us
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="showAddress"
                          checked={settings.footerSettings.companyInfo.showAddress}
                          onChange={(e) => setSettings({
                            ...settings,
                            footerSettings: {
                              ...settings.footerSettings,
                              companyInfo: { ...settings.footerSettings.companyInfo, showAddress: e.target.checked }
                            }
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="showAddress" className="mr-2 block text-sm text-gray-700">
                          إظهار العنوان
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="showPhone"
                          checked={settings.footerSettings.companyInfo.showPhone}
                          onChange={(e) => setSettings({
                            ...settings,
                            footerSettings: {
                              ...settings.footerSettings,
                              companyInfo: { ...settings.footerSettings.companyInfo, showPhone: e.target.checked }
                            }
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="showPhone" className="mr-2 block text-sm text-gray-700">
                          إظهار الهاتف
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="showEmail"
                          checked={settings.footerSettings.companyInfo.showEmail}
                          onChange={(e) => setSettings({
                            ...settings,
                            footerSettings: {
                              ...settings.footerSettings,
                              companyInfo: { ...settings.footerSettings.companyInfo, showEmail: e.target.checked }
                            }
                          })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="showEmail" className="mr-2 block text-sm text-gray-700">
                          إظهار البريد الإلكتروني
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* Quick Links */}
                  <div>
                    <h4 className="text-md font-medium text-gray-700 mb-3">الروابط السريعة</h4>
                    <div className="space-y-4">
                      {settings.footerSettings.quickLinks.map((link, index) => (
                        <div key={index} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الاسم بالعربية
                              </label>
                              <input
                                type="text"
                                value={link.nameAr}
                                onChange={(e) => {
                                  const newLinks = [...settings.footerSettings.quickLinks];
                                  newLinks[index] = { ...link, nameAr: e.target.value };
                                  setSettings({
                                    ...settings,
                                    footerSettings: { ...settings.footerSettings, quickLinks: newLinks }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="سياسة الخصوصية"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الاسم بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={link.nameEn}
                                onChange={(e) => {
                                  const newLinks = [...settings.footerSettings.quickLinks];
                                  newLinks[index] = { ...link, nameEn: e.target.value };
                                  setSettings({
                                    ...settings,
                                    footerSettings: { ...settings.footerSettings, quickLinks: newLinks }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Privacy Policy"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                الرابط
                              </label>
                              <input
                                type="text"
                                value={link.url}
                                onChange={(e) => {
                                  const newLinks = [...settings.footerSettings.quickLinks];
                                  newLinks[index] = { ...link, url: e.target.value };
                                  setSettings({
                                    ...settings,
                                    footerSettings: { ...settings.footerSettings, quickLinks: newLinks }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="/privacy"
                              />
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id={`link-active-${index}`}
                                checked={link.isActive}
                                onChange={(e) => {
                                  const newLinks = [...settings.footerSettings.quickLinks];
                                  newLinks[index] = { ...link, isActive: e.target.checked };
                                  setSettings({
                                    ...settings,
                                    footerSettings: { ...settings.footerSettings, quickLinks: newLinks }
                                  });
                                }}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              />
                              <label htmlFor={`link-active-${index}`} className="mr-2 block text-sm text-gray-700">
                                نشط
                              </label>
                            </div>

                            {settings.footerSettings.quickLinks.length > 1 && (
                              <button
                                type="button"
                                onClick={() => {
                                  const newLinks = settings.footerSettings.quickLinks.filter((_, i) => i !== index);
                                  setSettings({
                                    ...settings,
                                    footerSettings: { ...settings.footerSettings, quickLinks: newLinks }
                                  });
                                }}
                                className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                                title="حذف الرابط"
                              >
                                <i className="ri-delete-bin-line text-lg"></i>
                              </button>
                            )}
                          </div>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => {
                          setSettings({
                            ...settings,
                            footerSettings: {
                              ...settings.footerSettings,
                              quickLinks: [
                                ...settings.footerSettings.quickLinks,
                                { nameEn: '', nameAr: '', url: '', isActive: true }
                              ]
                            }
                          });
                        }}
                        className="w-full border-2 border-dashed border-gray-300 hover:border-blue-500 text-gray-600 hover:text-blue-600 py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
                      >
                        <i className="ri-add-line text-lg ml-2"></i>
                        إضافة رابط سريع جديد
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Contact Settings */}
              {activeTab === 'contact' && (
                <div className="space-y-8">
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات صفحة التواصل</h3>

                  {/* Basic Contact Info */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">معلومات التواصل الأساسية</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          البريد الإلكتروني
                        </label>
                        <input
                          type="email"
                          value={settings.contactEmail}
                          onChange={(e) => setSettings({...settings, contactEmail: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الهاتف
                        </label>
                        <input
                          type="tel"
                          value={settings.phone}
                          onChange={(e) => setSettings({...settings, phone: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="+966112345678"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الواتساب (مُهمل)
                        </label>
                        <input
                          type="tel"
                          value={settings.whatsappNumber}
                          onChange={(e) => setSettings({...settings, whatsappNumber: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-100"
                          placeholder="+966501234567"
                          disabled
                        />
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-2">
                          <div className="flex items-start">
                            <i className="ri-information-line text-yellow-600 text-sm ml-1 mt-0.5"></i>
                            <p className="text-sm text-yellow-700">
                              <strong>تنبيه:</strong> رقم الواتساب الآن يُدار من تبويب &quot;إعدادات التواصل&quot; مع إعدادات متقدمة للرسائل والتحكم الكامل.
                            </p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان
                        </label>
                        <textarea
                          value={settings.addressAr}
                          onChange={(e) => setSettings({...settings, addressAr: e.target.value})}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="الرياض، المملكة العربية السعودية"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Map Settings */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">إعدادات الخريطة</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="showMap"
                        checked={settings.contactSettings.mapSettings.showMap}
                        onChange={(e) => setSettings({
                          ...settings,
                          contactSettings: {
                            ...settings.contactSettings,
                            mapSettings: { ...settings.contactSettings.mapSettings, showMap: e.target.checked }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showMap" className="mr-2 block text-sm text-gray-700">
                        إظهار الخريطة في صفحة التواصل
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          خط العرض (Latitude)
                        </label>
                        <input
                          type="number"
                          step="any"
                          value={settings.contactSettings.mapSettings.latitude}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              mapSettings: { ...settings.contactSettings.mapSettings, latitude: parseFloat(e.target.value) || 0 }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="24.7136"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          خط الطول (Longitude)
                        </label>
                        <input
                          type="number"
                          step="any"
                          value={settings.contactSettings.mapSettings.longitude}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              mapSettings: { ...settings.contactSettings.mapSettings, longitude: parseFloat(e.target.value) || 0 }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="46.6753"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          مستوى التكبير (Zoom)
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="20"
                          value={settings.contactSettings.mapSettings.zoom}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              mapSettings: { ...settings.contactSettings.mapSettings, zoom: parseInt(e.target.value) || 15 }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="15"
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رابط Google Maps (اختياري)
                      </label>
                      <input
                        type="url"
                        value={settings.contactSettings.mapSettings.googleMapsUrl || ''}
                        onChange={(e) => setSettings({
                          ...settings,
                          contactSettings: {
                            ...settings.contactSettings,
                            mapSettings: { ...settings.contactSettings.mapSettings, googleMapsUrl: e.target.value }
                          }
                        })}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="https://maps.google.com/maps?q=24.7136,46.6753&z=15&output=embed"
                      />
                      <p className="text-sm text-gray-500 mt-1">
                        يمكنك الحصول على الرابط من Google Maps عبر النقر على &quot;مشاركة&quot; ثم &quot;تضمين خريطة&quot;
                      </p>
                    </div>
                  </div>

                  {/* Contact Form Settings */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">إعدادات نموذج التواصل</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="enableContactForm"
                        checked={settings.contactSettings.contactFormSettings.enableContactForm}
                        onChange={(e) => setSettings({
                          ...settings,
                          contactSettings: {
                            ...settings.contactSettings,
                            contactFormSettings: { ...settings.contactSettings.contactFormSettings, enableContactForm: e.target.checked }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableContactForm" className="mr-2 block text-sm text-gray-700">
                        تفعيل نموذج التواصل
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رسالة النجاح بالعربية
                        </label>
                        <textarea
                          value={settings.contactSettings.contactFormSettings.successMessageAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              contactFormSettings: { ...settings.contactSettings.contactFormSettings, successMessageAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="شكراً لك على رسالتك! سنتواصل معك قريباً."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رسالة النجاح بالإنجليزية
                        </label>
                        <textarea
                          value={settings.contactSettings.contactFormSettings.successMessage}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              contactFormSettings: { ...settings.contactSettings.contactFormSettings, successMessage: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="Thank you for your message! We will get back to you soon."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رسالة الخطأ بالعربية
                        </label>
                        <textarea
                          value={settings.contactSettings.contactFormSettings.errorMessageAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              contactFormSettings: { ...settings.contactSettings.contactFormSettings, errorMessageAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="حدث خطأ أثناء إرسال رسالتك. يرجى المحاولة مرة أخرى."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رسالة الخطأ بالإنجليزية
                        </label>
                        <textarea
                          value={settings.contactSettings.contactFormSettings.errorMessage}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              contactFormSettings: { ...settings.contactSettings.contactFormSettings, errorMessage: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="An error occurred while sending your message. Please try again."
                        />
                      </div>
                    </div>
                  </div>

                  {/* Office Hours Settings */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">ساعات العمل</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="enableOfficeHours"
                        checked={settings.contactSettings.officeHours.enabled}
                        onChange={(e) => setSettings({
                          ...settings,
                          contactSettings: {
                            ...settings.contactSettings,
                            officeHours: { ...settings.contactSettings.officeHours, enabled: e.target.checked }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableOfficeHours" className="mr-2 block text-sm text-gray-700">
                        إظهار ساعات العمل في صفحة التواصل
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          ساعات العمل بالعربية
                        </label>
                        <textarea
                          value={settings.contactSettings.officeHours.hoursTextAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              officeHours: { ...settings.contactSettings.officeHours, hoursTextAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="الأحد - الخميس: 8:00 صباحاً - 6:00 مساءً&#10;الجمعة: 2:00 ظهراً - 6:00 مساءً&#10;السبت: مغلق"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          ساعات العمل بالإنجليزية
                        </label>
                        <textarea
                          value={settings.contactSettings.officeHours.hoursText}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              officeHours: { ...settings.contactSettings.officeHours, hoursText: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="Sunday - Thursday: 8:00 AM - 6:00 PM&#10;Friday: 2:00 PM - 6:00 PM&#10;Saturday: Closed"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Additional Info Settings */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">معلومات إضافية</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="showFAQ"
                        checked={settings.contactSettings.additionalInfo.showFAQ}
                        onChange={(e) => setSettings({
                          ...settings,
                          contactSettings: {
                            ...settings.contactSettings,
                            additionalInfo: { ...settings.contactSettings.additionalInfo, showFAQ: e.target.checked }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="showFAQ" className="mr-2 block text-sm text-gray-700">
                        إظهار قسم الأسئلة الشائعة
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف صفحة التواصل بالعربية
                        </label>
                        <textarea
                          value={settings.contactSettings.additionalInfo.descriptionAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              additionalInfo: { ...settings.contactSettings.additionalInfo, descriptionAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="نحن هنا لمساعدتك في جميع استفساراتك واحتياجاتك. لا تتردد في التواصل معنا في أي وقت."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف صفحة التواصل بالإنجليزية
                        </label>
                        <textarea
                          value={settings.contactSettings.additionalInfo.description}
                          onChange={(e) => setSettings({
                            ...settings,
                            contactSettings: {
                              ...settings.contactSettings,
                              additionalInfo: { ...settings.contactSettings.additionalInfo, description: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="We are here to help you with all your inquiries and needs. Feel free to contact us anytime."
                        />
                      </div>
                    </div>
                  </div>

                  {/* Help Section */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">نصائح لإعداد صفحة التواصل:</p>
                        <ul className="list-disc list-inside space-y-1 text-blue-700">
                          <li>تأكد من صحة الإحداثيات الجغرافية لموقعك</li>
                          <li>اختبر رابط Google Maps للتأكد من عمله بشكل صحيح</li>
                          <li>اكتب رسائل واضحة ومفيدة للمستخدمين</li>
                          <li>حدث ساعات العمل بانتظام حسب أيام العطل والمناسبات</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* About Page Settings */}
              {activeTab === 'about' && (
                <div className="space-y-8">
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات صفحة من نحن</h3>

                  {/* Hero Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">قسم الهيرو</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان بالعربية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.heroSection.titleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              heroSection: { ...settings.aboutSettings.heroSection, titleAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="من نحن"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان بالإنجليزية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.heroSection.title}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              heroSection: { ...settings.aboutSettings.heroSection, title: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="About Us"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان الفرعي بالعربية
                        </label>
                        <textarea
                          value={settings.aboutSettings.heroSection.subtitleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              heroSection: { ...settings.aboutSettings.heroSection, subtitleAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="تعرف على شركة دروب هاجر المتخصصة في توفير أفضل تجهيزات الفنادق والمطاعم"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          العنوان الفرعي بالإنجليزية
                        </label>
                        <textarea
                          value={settings.aboutSettings.heroSection.subtitle}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              heroSection: { ...settings.aboutSettings.heroSection, subtitle: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="Learn about Droob Hajer, specialized in providing the best hotel and restaurant equipment"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Description Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">الوصف الرئيسي</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الوصف بالعربية
                        </label>
                        <textarea
                          value={settings.aboutSettings.description.textAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              description: { ...settings.aboutSettings.description, textAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={5}
                          placeholder="شركة دروب هاجر هي شركة رائدة في مجال توفير تجهيزات الفنادق والمطاعم..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الوصف بالإنجليزية
                        </label>
                        <textarea
                          value={settings.aboutSettings.description.text}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              description: { ...settings.aboutSettings.description, text: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={5}
                          placeholder="Droob Hajer is a leading company in providing hotel and restaurant equipment..."
                        />
                      </div>
                    </div>
                  </div>

                  {/* Vision & Mission */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">الرؤية والمهمة</h4>

                    {/* Vision */}
                    <div className="mb-6">
                      <h5 className="text-sm font-medium text-gray-700 mb-3">الرؤية</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            عنوان الرؤية بالعربية
                          </label>
                          <input
                            type="text"
                            value={settings.aboutSettings.vision.titleAr}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                vision: { ...settings.aboutSettings.vision, titleAr: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="رؤيتنا"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            عنوان الرؤية بالإنجليزية
                          </label>
                          <input
                            type="text"
                            value={settings.aboutSettings.vision.title}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                vision: { ...settings.aboutSettings.vision, title: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Our Vision"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نص الرؤية بالعربية
                          </label>
                          <textarea
                            value={settings.aboutSettings.vision.textAr}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                vision: { ...settings.aboutSettings.vision, textAr: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows={4}
                            placeholder="أن نكون الشركة الرائدة في المملكة العربية السعودية..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نص الرؤية بالإنجليزية
                          </label>
                          <textarea
                            value={settings.aboutSettings.vision.text}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                vision: { ...settings.aboutSettings.vision, text: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows={4}
                            placeholder="To be the leading company in Saudi Arabia..."
                          />
                        </div>
                      </div>
                    </div>

                    {/* Mission */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-3">المهمة</h5>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            عنوان المهمة بالعربية
                          </label>
                          <input
                            type="text"
                            value={settings.aboutSettings.mission.titleAr}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                mission: { ...settings.aboutSettings.mission, titleAr: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="مهمتنا"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            عنوان المهمة بالإنجليزية
                          </label>
                          <input
                            type="text"
                            value={settings.aboutSettings.mission.title}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                mission: { ...settings.aboutSettings.mission, title: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Our Mission"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نص المهمة بالعربية
                          </label>
                          <textarea
                            value={settings.aboutSettings.mission.textAr}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                mission: { ...settings.aboutSettings.mission, textAr: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows={4}
                            placeholder="نلتزم بتوفير معدات وتجهيزات عالية الجودة..."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            نص المهمة بالإنجليزية
                          </label>
                          <textarea
                            value={settings.aboutSettings.mission.text}
                            onChange={(e) => setSettings({
                              ...settings,
                              aboutSettings: {
                                ...settings.aboutSettings,
                                mission: { ...settings.aboutSettings.mission, text: e.target.value }
                              }
                            })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            rows={4}
                            placeholder="We are committed to providing high-quality equipment..."
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Values Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">القيم</h4>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان القيم بالعربية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.values.titleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              values: { ...settings.aboutSettings.values, titleAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="قيمنا"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان القيم بالإنجليزية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.values.title}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              values: { ...settings.aboutSettings.values, title: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Our Values"
                        />
                      </div>
                    </div>

                    {/* Values Items */}
                    <div className="space-y-4">
                      <h5 className="text-sm font-medium text-gray-700">عناصر القيم</h5>
                      {settings.aboutSettings.values.items.map((item, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                العنوان بالعربية
                              </label>
                              <input
                                type="text"
                                value={item.titleAr}
                                onChange={(e) => {
                                  const newItems = [...settings.aboutSettings.values.items];
                                  newItems[index] = { ...newItems[index], titleAr: e.target.value };
                                  setSettings({
                                    ...settings,
                                    aboutSettings: {
                                      ...settings.aboutSettings,
                                      values: { ...settings.aboutSettings.values, items: newItems }
                                    }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="الجودة"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                العنوان بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={item.titleEn}
                                onChange={(e) => {
                                  const newItems = [...settings.aboutSettings.values.items];
                                  newItems[index] = { ...newItems[index], titleEn: e.target.value };
                                  setSettings({
                                    ...settings,
                                    aboutSettings: {
                                      ...settings.aboutSettings,
                                      values: { ...settings.aboutSettings.values, items: newItems }
                                    }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Quality"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                الوصف بالعربية
                              </label>
                              <textarea
                                value={item.descriptionAr}
                                onChange={(e) => {
                                  const newItems = [...settings.aboutSettings.values.items];
                                  newItems[index] = { ...newItems[index], descriptionAr: e.target.value };
                                  setSettings({
                                    ...settings,
                                    aboutSettings: {
                                      ...settings.aboutSettings,
                                      values: { ...settings.aboutSettings.values, items: newItems }
                                    }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows={2}
                                placeholder="نلتزم بأعلى معايير الجودة..."
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                الوصف بالإنجليزية
                              </label>
                              <textarea
                                value={item.descriptionEn}
                                onChange={(e) => {
                                  const newItems = [...settings.aboutSettings.values.items];
                                  newItems[index] = { ...newItems[index], descriptionEn: e.target.value };
                                  setSettings({
                                    ...settings,
                                    aboutSettings: {
                                      ...settings.aboutSettings,
                                      values: { ...settings.aboutSettings.values, items: newItems }
                                    }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                rows={2}
                                placeholder="We are committed to the highest quality standards..."
                              />
                            </div>

                            <div className="md:col-span-2">
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                الأيقونة (Remix Icon)
                              </label>
                              <input
                                type="text"
                                value={item.icon}
                                onChange={(e) => {
                                  const newItems = [...settings.aboutSettings.values.items];
                                  newItems[index] = { ...newItems[index], icon: e.target.value };
                                  setSettings({
                                    ...settings,
                                    aboutSettings: {
                                      ...settings.aboutSettings,
                                      values: { ...settings.aboutSettings.values, items: newItems }
                                    }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="ri-award-line"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                استخدم أيقونات Remix Icon مثل: ri-award-line, ri-lightbulb-line, ri-shield-check-line
                              </p>
                            </div>
                          </div>

                          <button
                            type="button"
                            onClick={() => {
                              const newItems = settings.aboutSettings.values.items.filter((_, i) => i !== index);
                              setSettings({
                                ...settings,
                                aboutSettings: {
                                  ...settings.aboutSettings,
                                  values: { ...settings.aboutSettings.values, items: newItems }
                                }
                              });
                            }}
                            className="text-red-600 hover:text-red-700 text-sm font-medium flex items-center hover:bg-red-50 px-3 py-2 rounded-lg transition-colors duration-200"
                          >
                            <i className="ri-delete-bin-line text-lg ml-1"></i>
                            حذف هذه القيمة
                          </button>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => {
                          const newItem = {
                            titleEn: '',
                            titleAr: '',
                            descriptionEn: '',
                            descriptionAr: '',
                            icon: 'ri-star-line'
                          };
                          setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              values: {
                                ...settings.aboutSettings.values,
                                items: [...settings.aboutSettings.values.items, newItem]
                              }
                            }
                          });
                        }}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center hover:bg-blue-50 px-3 py-2 rounded-lg transition-colors duration-200"
                      >
                        <i className="ri-add-line text-lg ml-1"></i>
                        إضافة قيمة جديدة
                      </button>
                    </div>
                  </div>

                  {/* Team Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">الفريق</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان الفريق بالعربية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.team.titleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              team: { ...settings.aboutSettings.team, titleAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="فريقنا"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان الفريق بالإنجليزية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.team.title}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              team: { ...settings.aboutSettings.team, title: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Our Team"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف الفريق بالعربية
                        </label>
                        <textarea
                          value={settings.aboutSettings.team.descriptionAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              team: { ...settings.aboutSettings.team, descriptionAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="يضم فريقنا مجموعة من الخبراء والمتخصصين..."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف الفريق بالإنجليزية
                        </label>
                        <textarea
                          value={settings.aboutSettings.team.description}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              team: { ...settings.aboutSettings.team, description: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={4}
                          placeholder="Our team includes a group of experts and specialists..."
                        />
                      </div>
                    </div>
                  </div>

                  {/* Contact CTA Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">دعوة للتواصل</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="enableContactCTA"
                        checked={settings.aboutSettings.contactCTA.enabled}
                        onChange={(e) => setSettings({
                          ...settings,
                          aboutSettings: {
                            ...settings.aboutSettings,
                            contactCTA: { ...settings.aboutSettings.contactCTA, enabled: e.target.checked }
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enableContactCTA" className="mr-2 block text-sm text-gray-700">
                        إظهار قسم دعوة التواصل في نهاية الصفحة
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان دعوة التواصل بالعربية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.contactCTA.titleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              contactCTA: { ...settings.aboutSettings.contactCTA, titleAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="تواصل معنا"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان دعوة التواصل بالإنجليزية
                        </label>
                        <input
                          type="text"
                          value={settings.aboutSettings.contactCTA.title}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              contactCTA: { ...settings.aboutSettings.contactCTA, title: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Contact Us"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف دعوة التواصل بالعربية
                        </label>
                        <textarea
                          value={settings.aboutSettings.contactCTA.descriptionAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              contactCTA: { ...settings.aboutSettings.contactCTA, descriptionAr: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="نحن هنا لخدمتكم ومساعدتكم في جميع احتياجاتكم"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف دعوة التواصل بالإنجليزية
                        </label>
                        <textarea
                          value={settings.aboutSettings.contactCTA.description}
                          onChange={(e) => setSettings({
                            ...settings,
                            aboutSettings: {
                              ...settings.aboutSettings,
                              contactCTA: { ...settings.aboutSettings.contactCTA, description: e.target.value }
                            }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="We are here to serve you and help you with all your needs"
                        />
                      </div>
                    </div>
                  </div>



                  {/* Help Section */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">نصائح لإعداد صفحة &quot;من نحن&quot;:</p>
                        <ul className="list-disc list-inside space-y-1 text-blue-700">
                          <li>اكتب محتوى واضح ومفهوم يعكس هوية شركتك</li>
                          <li>استخدم أيقونات مناسبة للقيم من موقع Remix Icon</li>
                          <li>تأكد من ملء جميع الحقول باللغتين العربية والإنجليزية</li>
                          <li>اجعل الرؤية والمهمة محددة وقابلة للتحقيق</li>
                          <li>أضف شعارات واضحة وعالية الجودة للشركاء</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Partners Settings */}
              {activeTab === 'partners' && (
                <div className="space-y-8">
                  <h3 className="text-lg font-semibold text-gray-800">إعدادات الشركاء</h3>

                  {/* Partners Section */}
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h4 className="text-md font-medium text-gray-700 mb-4">قسم الشركاء</h4>

                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="enablePartners"
                        checked={settings.partnersSettings.enabled}
                        onChange={(e) => setSettings({
                          ...settings,
                          partnersSettings: { ...settings.partnersSettings, enabled: e.target.checked }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="enablePartners" className="mr-2 block text-sm text-gray-700">
                        إظهار قسم الشركاء في الصفحة الرئيسية
                      </label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان قسم الشركاء بالعربية
                        </label>
                        <input
                          type="text"
                          value={settings.partnersSettings.titleAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            partnersSettings: { ...settings.partnersSettings, titleAr: e.target.value }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="شركاؤنا"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان قسم الشركاء بالإنجليزية
                        </label>
                        <input
                          type="text"
                          value={settings.partnersSettings.title}
                          onChange={(e) => setSettings({
                            ...settings,
                            partnersSettings: { ...settings.partnersSettings, title: e.target.value }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Our Partners"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف قسم الشركاء بالعربية
                        </label>
                        <textarea
                          value={settings.partnersSettings.descriptionAr}
                          onChange={(e) => setSettings({
                            ...settings,
                            partnersSettings: { ...settings.partnersSettings, descriptionAr: e.target.value }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="نفخر بالعمل مع الشركات الرائدة في صناعة الضيافة"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          وصف قسم الشركاء بالإنجليزية
                        </label>
                        <textarea
                          value={settings.partnersSettings.description}
                          onChange={(e) => setSettings({
                            ...settings,
                            partnersSettings: { ...settings.partnersSettings, description: e.target.value }
                          })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          rows={3}
                          placeholder="We are proud to work with leading companies in the hospitality industry"
                        />
                      </div>
                    </div>

                    {/* Partners Items */}
                    <div className="space-y-4">
                      <h5 className="text-sm font-medium text-gray-700">قائمة الشركاء</h5>
                      {settings.partnersSettings.items.map((partner, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم الشريك بالعربية
                              </label>
                              <input
                                type="text"
                                value={partner.nameAr}
                                onChange={(e) => {
                                  const newItems = [...settings.partnersSettings.items];
                                  newItems[index] = { ...newItems[index], nameAr: e.target.value };
                                  setSettings({
                                    ...settings,
                                    partnersSettings: { ...settings.partnersSettings, items: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="فندق الرياض الكبير"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                اسم الشريك بالإنجليزية
                              </label>
                              <input
                                type="text"
                                value={partner.nameEn}
                                onChange={(e) => {
                                  const newItems = [...settings.partnersSettings.items];
                                  newItems[index] = { ...newItems[index], nameEn: e.target.value };
                                  setSettings({
                                    ...settings,
                                    partnersSettings: { ...settings.partnersSettings, items: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Riyadh Grand Hotel"
                              />
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                شعار الشريك (رابط الصورة)
                              </label>
                              <input
                                type="url"
                                value={partner.logo}
                                onChange={(e) => {
                                  const newItems = [...settings.partnersSettings.items];
                                  newItems[index] = { ...newItems[index], logo: e.target.value };
                                  setSettings({
                                    ...settings,
                                    partnersSettings: { ...settings.partnersSettings, items: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="https://example.com/logo.png"
                              />
                              {partner.logo && (
                                <div className="mt-2">
                                  <img
                                    src={partner.logo}
                                    alt={partner.nameAr}
                                    className="w-20 h-10 object-cover rounded border"
                                    onError={(e) => {
                                      (e.target as HTMLImageElement).src = 'https://via.placeholder.com/80x40?text=شعار';
                                    }}
                                  />
                                </div>
                              )}
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                موقع الشريك (اختياري)
                              </label>
                              <input
                                type="url"
                                value={partner.website || ''}
                                onChange={(e) => {
                                  const newItems = [...settings.partnersSettings.items];
                                  newItems[index] = { ...newItems[index], website: e.target.value };
                                  setSettings({
                                    ...settings,
                                    partnersSettings: { ...settings.partnersSettings, items: newItems }
                                  });
                                }}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                placeholder="https://partner-website.com"
                              />
                            </div>
                          </div>

                          <button
                            type="button"
                            onClick={() => {
                              const newItems = settings.partnersSettings.items.filter((_, i) => i !== index);
                              setSettings({
                                ...settings,
                                partnersSettings: { ...settings.partnersSettings, items: newItems }
                              });
                            }}
                            className="text-red-600 hover:text-red-700 text-sm font-medium flex items-center hover:bg-red-50 px-3 py-2 rounded-lg transition-colors duration-200"
                          >
                            <i className="ri-delete-bin-line text-lg ml-1"></i>
                            حذف هذا الشريك
                          </button>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => {
                          const newPartner = {
                            nameEn: '',
                            nameAr: '',
                            logo: '',
                            website: '',
                            description: '',
                            descriptionAr: ''
                          };
                          setSettings({
                            ...settings,
                            partnersSettings: {
                              ...settings.partnersSettings,
                              items: [...settings.partnersSettings.items, newPartner]
                            }
                          });
                        }}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center hover:bg-blue-50 px-3 py-2 rounded-lg transition-colors duration-200"
                      >
                        <i className="ri-add-line text-lg ml-1"></i>
                        إضافة شريك جديد
                      </button>
                    </div>

                    {/* Help Section */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
                      <div className="flex items-start">
                        <i className="ri-information-line text-blue-600 text-lg ml-2 mt-0.5"></i>
                        <div className="text-sm text-blue-800">
                          <p className="font-medium mb-1">نصائح لإدارة الشركاء:</p>
                          <ul className="list-disc list-inside space-y-1 text-blue-700">
                            <li>استخدم شعارات عالية الجودة وواضحة</li>
                            <li>تأكد من أن الشعارات بنفس الحجم تقريباً</li>
                            <li>أضف روابط مواقع الشركاء إذا كانت متاحة</li>
                            <li>رتب الشركاء حسب الأهمية أو الترتيب الأبجدي</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Hero Images Settings */}
              {activeTab === 'hero' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">صور الهيرو</h3>
                  <p className="text-gray-600">إدارة الصور المعروضة في القسم الرئيسي للموقع</p>

                  <HeroImageUpload
                    images={settings.heroImages.filter(img => img.trim() !== '')}
                    onImagesChange={(newImages) => {
                      setSettings(prev => ({
                        ...prev,
                        heroImages: newImages.length > 0 ? newImages : ['']
                      }));
                    }}
                    maxImages={5}
                  />
                </div>
              )}

              {/* About Settings */}
              {activeTab === 'about' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-800">نبذة عن الشركة</h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نبذة عن الشركة بالعربية
                    </label>
                    <textarea
                      value={settings.aboutTextAr}
                      onChange={(e) => setSettings({...settings, aboutTextAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={6}
                      placeholder="اكتب نبذة عن الشركة بالعربية..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نبذة عن الشركة بالإنجليزية
                    </label>
                    <textarea
                      value={settings.aboutText}
                      onChange={(e) => setSettings({...settings, aboutText: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={6}
                      placeholder="Write about your company in English..."
                    />
                  </div>

                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-lightbulb-line text-gray-600 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-gray-700">
                        <p className="font-medium mb-1">نصائح لكتابة نبذة فعالة:</p>
                        <ul className="list-disc list-inside space-y-1">
                          <li>اذكر تاريخ تأسيس الشركة وخبرتها</li>
                          <li>وضح الخدمات والمنتجات التي تقدمها</li>
                          <li>أبرز نقاط القوة والمميزات التنافسية</li>
                          <li>اذكر رؤية ورسالة الشركة</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Communication Settings */}
              {activeTab === 'communication' && (
                <div className="space-y-8">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800">إعدادات التواصل</h3>
                    <p className="text-gray-600">إعدادات الإيميل والواتساب لطلبات التسعير والتواصل مع العملاء</p>
                  </div>

                  {/* Email Settings Section */}
                  <div className="bg-gray-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h4 className="text-md font-semibold text-gray-800 flex items-center gap-2">
                          <i className="ri-mail-line text-blue-600"></i>
                          إعدادات الإيميل
                        </h4>
                        <p className="text-gray-600 text-sm">إعدادات إرسال الإيميل التلقائي لطلبات التسعير</p>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="emailEnabled"
                          checked={settings.communicationSettings?.email?.enabled || false}
                          onChange={(e) => updateCommunicationSettings('email', { enabled: e.target.checked })}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="emailEnabled" className="mr-2 block text-sm text-gray-700">
                          تفعيل إرسال الإيميل التلقائي
                        </label>
                      </div>
                    </div>

                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="flex items-start">
                      <i className="ri-information-line text-orange-500 text-lg ml-2 mt-0.5"></i>
                      <div className="text-sm text-orange-700">
                        <p className="font-medium mb-1">إعدادات Titan Email:</p>
                        <p>سيتم إرسال طلبات التسعير من إيميلك إلى إيميلك باستخدام خادم smtp.titan.email. الرد سيذهب مباشرة لإيميل العميل.</p>
                      </div>
                    </div>
                  </div>

                  {/* SMTP Settings Component */}
                  <SMTPSettingsComponent />
                  </div>

                  {/* WhatsApp Settings Section */}
                  <div className="bg-green-50 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div>
                        <h4 className="text-md font-semibold text-gray-800 flex items-center gap-2">
                          <i className="ri-whatsapp-line text-green-600"></i>
                          إعدادات الواتساب
                        </h4>
                        <p className="text-gray-600 text-sm">إعدادات رقم الواتساب ورسائل التواصل مع العملاء</p>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="whatsappEnabled"
                          checked={settings.communicationSettings?.whatsapp?.enabled || false}
                          onChange={(e) => updateCommunicationSettings('whatsapp', { enabled: e.target.checked })}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <label htmlFor="whatsappEnabled" className="mr-2 block text-sm text-gray-700">
                          تفعيل الواتساب
                        </label>
                      </div>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الواتساب التجاري
                        </label>
                        <input
                          type="tel"
                          value={settings.communicationSettings?.whatsapp?.businessNumber || ''}
                          onChange={(e) => updateCommunicationSettings('whatsapp', { businessNumber: e.target.value })}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="+966501234567"
                        />
                        <p className="text-sm text-gray-500 mt-1">
                          الرقم المستخدم لاستقبال رسائل العملاء وإرسال الردود (يجب أن يتضمن رمز الدولة)
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            رسالة الترحيب بالعربية
                          </label>
                          <textarea
                            value={settings.communicationSettings?.whatsapp?.welcomeMessageAr || ''}
                            onChange={(e) => updateCommunicationSettings('whatsapp', { welcomeMessageAr: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            rows={3}
                            placeholder="مرحباً! كيف يمكننا مساعدتك اليوم؟"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            رسالة الترحيب بالإنجليزية
                          </label>
                          <textarea
                            value={settings.communicationSettings?.whatsapp?.welcomeMessage || ''}
                            onChange={(e) => updateCommunicationSettings('whatsapp', { welcomeMessage: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            rows={3}
                            placeholder="Hello! How can we help you today?"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            رسالة الرد على طلب التسعير بالعربية
                          </label>
                          <textarea
                            value={settings.communicationSettings?.whatsapp?.quoteResponseMessageAr || ''}
                            onChange={(e) => updateCommunicationSettings('whatsapp', { quoteResponseMessageAr: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            rows={3}
                            placeholder="شكراً لك على طلب التسعير. سنتواصل معك قريباً بعرضنا."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            رسالة الرد على طلب التسعير بالإنجليزية
                          </label>
                          <textarea
                            value={settings.communicationSettings?.whatsapp?.quoteResponseMessage || ''}
                            onChange={(e) => updateCommunicationSettings('whatsapp', { quoteResponseMessage: e.target.value })}
                            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                            rows={3}
                            placeholder="Thank you for your quote request. We will contact you soon with our offer."
                          />
                        </div>
                      </div>

                      <div className="bg-green-100 border border-green-200 rounded-lg p-4">
                        <div className="flex items-start">
                          <i className="ri-information-line text-green-600 text-lg ml-2 mt-0.5"></i>
                          <div className="text-sm text-green-700">
                            <p className="font-medium mb-2">كيفية استخدام إعدادات الواتساب:</p>
                            <ul className="list-disc list-inside space-y-1">
                              <li><strong>رقم الواتساب التجاري:</strong> يُستخدم في جميع أزرار الواتساب في الموقع</li>
                              <li><strong>رسالة الترحيب:</strong> تظهر عند الضغط على أيقونة الواتساب العامة</li>
                              <li><strong>رسالة طلب التسعير:</strong> تُرسل للعملاء عند التواصل معهم من لوحة التحكم</li>
                              <li>سيتم تحديث جميع أزرار الواتساب في الموقع تلقائياً</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default SettingsAdmin;
